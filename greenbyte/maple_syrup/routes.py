from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash
from flask_login import login_required, current_user
from datetime import datetime, timedelta
from greenbyte.maple_syrup import maple_syrup

@maple_syrup.route("/maple_syrup")
@login_required
def index():
    """Main maple syrup tracking page."""
    return render_template('maple_syrup/page_maple_syrup.html', title='Maple Syrup Tracking')

@maple_syrup.route("/maple_syrup/sap_collection")
@login_required
def sap_collection():
    """Sap collection tracking page."""
    return render_template('maple_syrup/sap_collection.html', title='Sap Collection')

@maple_syrup.route("/maple_syrup/boiling_sessions")
@login_required
def boiling_sessions():
    """Boiling sessions tracking page."""
    return render_template('maple_syrup/boiling_sessions.html', title='Boiling Sessions')

@maple_syrup.route("/maple_syrup/production")
@login_required
def production():
    """Syrup production tracking page."""
    return render_template('maple_syrup/production.html', title='Syrup Production')

@maple_syrup.route("/maple_syrup/equipment")
@login_required
def equipment():
    """Equipment status tracking page."""
    return render_template('maple_syrup/equipment.html', title='Equipment Status')
