{% extends "layout.html" %}
{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Edit Farm: {{ farm.name }}</h1>
        <div>
            <a href="{{ url_for('commercial.farm_detail', farm_id=farm.id) }}" class="d-none d-sm-inline-block btn btn-sm btn-outline-secondary shadow-sm">
                <i class="fas fa-arrow-left fa-sm mr-1"></i> Back to Farm
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">Farm Details</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="">
                        {{ form.hidden_tag() }}
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                {{ form.name.label(class="form-control-label") }}
                                {% if form.name.errors %}
                                    {{ form.name(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.name.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.name(class="form-control") }}
                                {% endif %}
                            </div>
                            <div class="form-group col-md-6">
                                {{ form.business_name.label(class="form-control-label") }}
                                {% if form.business_name.errors %}
                                    {{ form.business_name(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.business_name.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.business_name(class="form-control") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="form-group">
                            {{ form.location.label(class="form-control-label") }}
                            {% if form.location.errors %}
                                {{ form.location(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.location.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.location(class="form-control") }}
                            {% endif %}
                        </div>

                        <div class="form-row">
                            <div class="form-group col-md-6">
                                {{ form.phone.label(class="form-control-label") }}
                                {% if form.phone.errors %}
                                    {{ form.phone(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.phone.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.phone(class="form-control") }}
                                {% endif %}
                            </div>
                            <div class="form-group col-md-6">
                                {{ form.email.label(class="form-control-label") }}
                                {% if form.email.errors %}
                                    {{ form.email(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.email.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.email(class="form-control") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group col-md-6">
                                {{ form.website.label(class="form-control-label") }}
                                {% if form.website.errors %}
                                    {{ form.website(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.website.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.website(class="form-control") }}
                                {% endif %}
                            </div>
                            <div class="form-group col-md-6">
                                {{ form.tax_id.label(class="form-control-label") }}
                                {% if form.tax_id.errors %}
                                    {{ form.tax_id(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.tax_id.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.tax_id(class="form-control") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="form-group">
                            {{ form.description.label(class="form-control-label") }}
                            {% if form.description.errors %}
                                {{ form.description(class="form-control is-invalid", rows=5) }}
                                <div class="invalid-feedback">
                                    {% for error in form.description.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.description(class="form-control", rows=5) }}
                            {% endif %}
                        </div>

                        <div class="form-group">
                            {{ form.submit(class="btn btn-success") }}
                            <a href="{{ url_for('commercial.farm_hub', farm_id=farm.id) }}" class="btn btn-outline-secondary">Cancel</a>
                            <!-- Delete button with confirmation modal trigger -->
                            <button type="button" class="btn btn-outline-danger" data-toggle="modal" data-target="#deleteFarmModal">
                                <i class="fas fa-trash-alt"></i> Delete Farm
                            </button>
                        </div>

                    </form>

                    <!-- Delete Confirmation Modal -->
                    <div class="modal fade" id="deleteFarmModal" tabindex="-1" aria-labelledby="deleteFarmModalLabel" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="deleteFarmModalLabel">Confirm Deletion</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <p>Are you sure you want to delete <strong>{{ farm.name }}</strong>?</p>
                                    <p class="text-danger"><strong>Warning:</strong> This action cannot be undone. All farm data, including gardens, zones, plants, and member associations will be permanently deleted.</p>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                    <form action="{{ url_for('commercial.delete_farm', farm_id=farm.id) }}" method="POST" style="display: inline;">
                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                        <button type="submit" class="btn btn-danger">Delete Farm</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">Farm Management Tips</h6>
                </div>
                <div class="card-body">
                    <p>Updating your farm details helps:</p>
                    <ul>
                        <li>Keep your business information current</li>
                        <li>Provide accurate contact information for clients</li>
                        <li>Maintain professional business records</li>
                    </ul>

                    <hr>

                    <h6 class="font-weight-bold">Farm Details</h6>
                    <p><strong>Farm Name:</strong> The name displayed throughout the system.</p>
                    <p><strong>Business Name:</strong> Your legal business entity name (if different).</p>
                    <p><strong>Tax ID:</strong> For business records and reporting.</p>

                    <hr>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle mr-1"></i> After updating your farm details, you can manage team members, inventory, and gardens from the farm dashboard.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize the delete modal using Bootstrap 5
        const deleteButton = document.querySelector('[data-target="#deleteFarmModal"]');
        const deleteModal = document.getElementById('deleteFarmModal');

        if (deleteButton && deleteModal) {
            // Update the button to use Bootstrap 5 data-bs-* attributes
            deleteButton.setAttribute('data-bs-toggle', 'modal');
            deleteButton.setAttribute('data-bs-target', '#deleteFarmModal');
            deleteButton.removeAttribute('data-toggle');
            deleteButton.removeAttribute('data-target');

            // Initialize the modal
            const modal = new bootstrap.Modal(deleteModal);

            // Add click event listener to the button
            deleteButton.addEventListener('click', function() {
                modal.show();
            });

            // Update close button to use Bootstrap 5 data-bs-* attributes
            const closeButton = deleteModal.querySelector('.close');
            if (closeButton) {
                closeButton.setAttribute('data-bs-dismiss', 'modal');
                closeButton.removeAttribute('data-dismiss');
            }

            // Update cancel button to use Bootstrap 5 data-bs-* attributes
            const cancelButton = deleteModal.querySelector('.modal-footer .btn-secondary');
            if (cancelButton) {
                cancelButton.setAttribute('data-bs-dismiss', 'modal');
                cancelButton.removeAttribute('data-dismiss');
            }
        }
    });
</script>
{% endblock %}
