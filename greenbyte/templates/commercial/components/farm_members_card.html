<!-- Farm Members Card -->
<div class="card shadow mb-4 rounded-custom">
  <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
    <h6 class="m-0 font-weight-bold text-success">Farm Members</h6>
    {% if selected_farm and (current_user.is_super_user() or selected_farm.is_admin(current_user.id) or selected_farm.owner_id == current_user.id) %}
    <a href="{{ url_for('commercial.add_farm_member', farm_id=selected_farm.id) }}" class="btn btn-sm btn-success">
      <i class="fas fa-user-plus fa-sm"></i>
    </a>
    {% endif %}
  </div>
  <div class="card-body">
    {% if selected_farm %}
      <div class="list-group">
        <!-- Owner -->
        {% if selected_farm.owner %}
        <div class="list-group-item d-flex align-items-center py-3">
          <div class="mr-3">
            <div class="icon-circle bg-success">
              <i class="fas fa-user-tie text-white"></i>
            </div>
          </div>
          <div>
            <h6 class="mb-0">{{ selected_farm.owner.username }}</h6>
            <span class="badge badge-success">Owner</span>
          </div>
        </div>
        {% endif %}
        <!-- Admin Members -->
        {% if admins %}
          {% for admin in admins %}
            {% if admin.id != selected_farm.owner_id %}
            <div class="list-group-item d-flex align-items-center py-3">
              <div class="mr-3">
                <div class="icon-circle bg-info">
                  <i class="fas fa-user-shield text-white"></i>
                </div>
              </div>
              <div>
                <h6 class="mb-0">{{ admin.username }}</h6>
                <span class="badge badge-info">Admin</span>
              </div>
            </div>
            {% endif %}
          {% endfor %}
        {% endif %}

        <!-- Gardener Members -->
        {% if gardeners %}
          {% for gardener in gardeners %}
            {% if gardener.id != selected_farm.owner_id %}
            <div class="list-group-item d-flex align-items-center py-3">
              <div class="mr-3">
                <div class="icon-circle bg-light">
                  <i class="fas fa-user text-success"></i>
                </div>
              </div>
              <div>
                <h6 class="mb-0">{{ gardener.username }}</h6>
                <span class="badge badge-success">Gardener</span>
              </div>
            </div>
            {% endif %}
          {% endfor %}
        {% endif %}

        <!-- Regular Members (fallback) -->
        {% if selected_farm.members and not (admins or gardeners) %}
          {% for member in selected_farm.members %}
            {% if member.id != selected_farm.owner_id %}
            <div class="list-group-item d-flex align-items-center py-3">
              <div class="mr-3">
                <div class="icon-circle bg-light">
                  <i class="fas fa-user text-success"></i>
                </div>
              </div>
              <div>
                <h6 class="mb-0">{{ member.username }}</h6>
                <span class="badge badge-{{ 'info' if selected_farm.is_admin(member.id) else 'success' if selected_farm.is_gardener(member.id) else 'light' }}">
                  {{ selected_farm.get_member_role(member.id)|capitalize if selected_farm.get_member_role(member.id) else 'Member' }}
                </span>
              </div>
            </div>
            {% endif %}
          {% endfor %}
        {% endif %}
      </div>
    {% else %}
      <div class="text-center py-3">
        <p class="mb-0">Select a farm to view members</p>
      </div>
    {% endif %}
  </div>
</div>
