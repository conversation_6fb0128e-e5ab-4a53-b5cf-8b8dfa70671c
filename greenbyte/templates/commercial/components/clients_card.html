<!-- Clients Card -->
<div class="card shadow mb-4 rounded-custom">
  <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
    <h6 class="m-0 font-weight-bold text-success">Top Clients</h6>
    <div class="dropdown no-arrow">
      <a class="dropdown-toggle" href="#" role="button" id="clientsDropdownMenu" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
        <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
      </a>
      <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="clientsDropdownMenu">
        <div class="dropdown-header">Actions:</div>
        <a class="dropdown-item" href="#"><i class="fas fa-user-plus fa-sm fa-fw mr-2 text-success"></i>Add Client</a>
        <a class="dropdown-item" href="#"><i class="fas fa-envelope fa-sm fa-fw mr-2 text-success"></i>Email All</a>
        <div class="dropdown-divider"></div>
        <a class="dropdown-item" href="#"><i class="fas fa-users fa-sm fa-fw mr-2 text-success"></i>View All Clients</a>
      </div>
    </div>
  </div>
  <div class="card-body">
    {% if selected_farm %}
      <div class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <th>Client</th>
              <th>Type</th>
              <th>Orders</th>
              <th>Revenue</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <!-- Sample clients - would be replaced with actual data -->
            <tr>
              <td>
                <div class="d-flex align-items-center">
                  <div class="mr-3">
                    <div class="icon-circle bg-primary">
                      <i class="fas fa-store text-white"></i>
                    </div>
                  </div>
                  <div>
                    <h6 class="mb-0">Local Grocery Co.</h6>
                    <small class="text-muted">Since Jan 2023</small>
                  </div>
                </div>
              </td>
              <td><span class="badge badge-light">Retail</span></td>
              <td>12</td>
              <td>$2,450.75</td>
              <td><span class="badge badge-success">Active</span></td>
              <td>
                <div class="btn-group btn-group-sm">
                  <button type="button" class="btn btn-outline-success btn-sm" title="View">
                    <i class="fas fa-eye"></i>
                  </button>
                  <button type="button" class="btn btn-outline-primary btn-sm" title="Contact">
                    <i class="fas fa-envelope"></i>
                  </button>
                </div>
              </td>
            </tr>
            <tr>
              <td>
                <div class="d-flex align-items-center">
                  <div class="mr-3">
                    <div class="icon-circle bg-info">
                      <i class="fas fa-utensils text-white"></i>
                    </div>
                  </div>
                  <div>
                    <h6 class="mb-0">Farm to Table Restaurant</h6>
                    <small class="text-muted">Since Mar 2023</small>
                  </div>
                </div>
              </td>
              <td><span class="badge badge-light">Restaurant</span></td>
              <td>8</td>
              <td>$1,875.50</td>
              <td><span class="badge badge-success">Active</span></td>
              <td>
                <div class="btn-group btn-group-sm">
                  <button type="button" class="btn btn-outline-success btn-sm" title="View">
                    <i class="fas fa-eye"></i>
                  </button>
                  <button type="button" class="btn btn-outline-primary btn-sm" title="Contact">
                    <i class="fas fa-envelope"></i>
                  </button>
                </div>
              </td>
            </tr>
            <tr>
              <td>
                <div class="d-flex align-items-center">
                  <div class="mr-3">
                    <div class="icon-circle bg-success">
                      <i class="fas fa-shopping-basket text-white"></i>
                    </div>
                  </div>
                  <div>
                    <h6 class="mb-0">Community Market</h6>
                    <small class="text-muted">Since Feb 2023</small>
                  </div>
                </div>
              </td>
              <td><span class="badge badge-light">Market</span></td>
              <td>15</td>
              <td>$3,120.25</td>
              <td><span class="badge badge-success">Active</span></td>
              <td>
                <div class="btn-group btn-group-sm">
                  <button type="button" class="btn btn-outline-success btn-sm" title="View">
                    <i class="fas fa-eye"></i>
                  </button>
                  <button type="button" class="btn btn-outline-primary btn-sm" title="Contact">
                    <i class="fas fa-envelope"></i>
                  </button>
                </div>
              </td>
            </tr>
            <tr>
              <td>
                <div class="d-flex align-items-center">
                  <div class="mr-3">
                    <div class="icon-circle bg-warning">
                      <i class="fas fa-store-alt text-white"></i>
                    </div>
                  </div>
                  <div>
                    <h6 class="mb-0">Organic Food Store</h6>
                    <small class="text-muted">Since Apr 2023</small>
                  </div>
                </div>
              </td>
              <td><span class="badge badge-light">Retail</span></td>
              <td>6</td>
              <td>$950.50</td>
              <td><span class="badge badge-warning">Pending</span></td>
              <td>
                <div class="btn-group btn-group-sm">
                  <button type="button" class="btn btn-outline-success btn-sm" title="View">
                    <i class="fas fa-eye"></i>
                  </button>
                  <button type="button" class="btn btn-outline-primary btn-sm" title="Contact">
                    <i class="fas fa-envelope"></i>
                  </button>
                </div>
              </td>
            </tr>
            <tr>
              <td>
                <div class="d-flex align-items-center">
                  <div class="mr-3">
                    <div class="icon-circle bg-secondary">
                      <i class="fas fa-users text-white"></i>
                    </div>
                  </div>
                  <div>
                    <h6 class="mb-0">Weekly Farmers Market</h6>
                    <small class="text-muted">Since Jan 2023</small>
                  </div>
                </div>
              </td>
              <td><span class="badge badge-light">Market</span></td>
              <td>20</td>
              <td>$4,125.75</td>
              <td><span class="badge badge-secondary">Inactive</span></td>
              <td>
                <div class="btn-group btn-group-sm">
                  <button type="button" class="btn btn-outline-success btn-sm" title="View">
                    <i class="fas fa-eye"></i>
                  </button>
                  <button type="button" class="btn btn-outline-primary btn-sm" title="Contact">
                    <i class="fas fa-envelope"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="text-center mt-3">
        <a href="#" class="btn btn-sm btn-success">View All Clients</a>
      </div>
    {% else %}
      <div class="text-center py-4">
        <i class="fas fa-users fa-3x text-gray-300 mb-3"></i>
        <p>Select a farm to view clients</p>
      </div>
    {% endif %}
  </div>
</div>
