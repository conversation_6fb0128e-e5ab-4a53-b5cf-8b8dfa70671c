<!-- Farm Details Card -->
<div class="card shadow mb-4 rounded-custom">
  <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
    <h6 class="m-0 font-weight-bold text-success">Farm Details</h6>
    {% if selected_farm and (current_user.is_super_user() or selected_farm.is_admin(current_user.id) or selected_farm.owner_id == current_user.id) %}
    <a href="{{ url_for('commercial.edit_farm', farm_id=selected_farm.id) }}" class="btn btn-sm btn-outline-success">
      <i class="fas fa-edit fa-sm"></i> Edit
    </a>
    {% endif %}
  </div>
  <div class="card-body">
    {% if selected_farm %}
      <div class="d-flex align-items-center mb-3">
        <div class="mr-3">
          <i class="fas fa-farm fa-2x text-success"></i>
        </div>
        <div>
          <h5 class="mb-0">{{ selected_farm.name }}</h5>
          {% if selected_farm.business_name %}
          <p class="text-muted mb-0">{{ selected_farm.business_name }}</p>
          {% endif %}
        </div>
      </div>

      <hr>

      <div class="mb-3">
        <h6 class="font-weight-bold">Location</h6>
        <p class="mb-0">{{ selected_farm.location or 'No address provided' }}</p>
      </div>

      {% if selected_farm.description %}
      <hr>
      <div class="mb-3">
        <h6 class="font-weight-bold">Description</h6>
        <p>{{ selected_farm.description }}</p>
      </div>
      {% endif %}

      <hr>

      <div class="mb-3">
        <h6 class="font-weight-bold">Farm Stats</h6>
        <div class="row mt-2">
          <div class="col-6">
            <div class="stat-item">
              <span class="stat-label">Gardens:</span>
              <span class="stat-value">{{ selected_farm.gardens|length if selected_farm.gardens else 0 }}</span>
            </div>
          </div>
          <div class="col-6">
            <div class="stat-item">
              <span class="stat-label">Members:</span>
              <span class="stat-value">{{ (selected_farm.members|length) + 1 }}</span>
            </div>
          </div>
        </div>
      </div>
    {% else %}
      <div class="text-center py-4">
        <i class="fas fa-seedling fa-3x text-gray-300 mb-3"></i>
        <p>You don't have any farms yet.</p>
        {% if current_user.is_super_user() or current_user.access_level == 'admin' or current_user.access_level == 'superadmin' %}
          <a href="{{ url_for('commercial.create_farm') }}" class="btn btn-success">
            <i class="fas fa-plus fa-sm"></i> Create Your First Farm
          </a>
        {% else %}
          <p class="text-muted small">Contact an administrator to be added to a farm.</p>
        {% endif %}
      </div>
    {% endif %}
  </div>
</div>
