<!-- Inventory Card -->
<div class="card shadow mb-4 rounded-custom">
  <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
    <h6 class="m-0 font-weight-bold text-success">Inventory</h6>
    <div class="dropdown no-arrow">
      <a class="dropdown-toggle" href="#" role="button" id="inventoryDropdownMenu" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
        <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
      </a>
      <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="inventoryDropdownMenu">
        <div class="dropdown-header">Actions:</div>
        <a class="dropdown-item" href="#"><i class="fas fa-plus fa-sm fa-fw mr-2 text-success"></i>Add Item</a>
        <a class="dropdown-item" href="#"><i class="fas fa-file-export fa-sm fa-fw mr-2 text-success"></i>Export</a>
        <div class="dropdown-divider"></div>
        <a class="dropdown-item" href="#"><i class="fas fa-sync-alt fa-sm fa-fw mr-2 text-gray-400"></i>Refresh</a>
      </div>
    </div>
  </div>
  <div class="card-body">
    {% if selected_farm %}
      <div class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <th>Item</th>
              <th>Category</th>
              <th>Quantity</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <!-- Sample inventory items - would be replaced with actual data -->
            <tr>
              <td>
                <div class="d-flex align-items-center">
                  <div class="mr-2">
                    <i class="fas fa-seedling text-success"></i>
                  </div>
                  <span>Tomato Seeds</span>
                </div>
              </td>
              <td><span class="badge badge-light">Seeds</span></td>
              <td>250 g</td>
              <td><span class="badge badge-success">In Stock</span></td>
              <td>
                <div class="btn-group btn-group-sm">
                  <button type="button" class="btn btn-outline-success btn-sm" title="Edit">
                    <i class="fas fa-edit"></i>
                  </button>
                  <button type="button" class="btn btn-outline-danger btn-sm" title="Delete">
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
              </td>
            </tr>
            <tr>
              <td>
                <div class="d-flex align-items-center">
                  <div class="mr-2">
                    <i class="fas fa-leaf text-success"></i>
                  </div>
                  <span>Organic Fertilizer</span>
                </div>
              </td>
              <td><span class="badge badge-light">Supplies</span></td>
              <td>5 kg</td>
              <td><span class="badge badge-success">In Stock</span></td>
              <td>
                <div class="btn-group btn-group-sm">
                  <button type="button" class="btn btn-outline-success btn-sm" title="Edit">
                    <i class="fas fa-edit"></i>
                  </button>
                  <button type="button" class="btn btn-outline-danger btn-sm" title="Delete">
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
              </td>
            </tr>
            <tr>
              <td>
                <div class="d-flex align-items-center">
                  <div class="mr-2">
                    <i class="fas fa-tint text-info"></i>
                  </div>
                  <span>Irrigation Pipes</span>
                </div>
              </td>
              <td><span class="badge badge-light">Equipment</span></td>
              <td>12 units</td>
              <td><span class="badge badge-warning">Low Stock</span></td>
              <td>
                <div class="btn-group btn-group-sm">
                  <button type="button" class="btn btn-outline-success btn-sm" title="Edit">
                    <i class="fas fa-edit"></i>
                  </button>
                  <button type="button" class="btn btn-outline-danger btn-sm" title="Delete">
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
              </td>
            </tr>
            <tr>
              <td>
                <div class="d-flex align-items-center">
                  <div class="mr-2">
                    <i class="fas fa-box text-warning"></i>
                  </div>
                  <span>Harvest Crates</span>
                </div>
              </td>
              <td><span class="badge badge-light">Packaging</span></td>
              <td>8 units</td>
              <td><span class="badge badge-danger">Out of Stock</span></td>
              <td>
                <div class="btn-group btn-group-sm">
                  <button type="button" class="btn btn-outline-success btn-sm" title="Edit">
                    <i class="fas fa-edit"></i>
                  </button>
                  <button type="button" class="btn btn-outline-danger btn-sm" title="Delete">
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
              </td>
            </tr>
            <tr>
              <td>
                <div class="d-flex align-items-center">
                  <div class="mr-2">
                    <i class="fas fa-spray-can text-secondary"></i>
                  </div>
                  <span>Organic Pest Control</span>
                </div>
              </td>
              <td><span class="badge badge-light">Supplies</span></td>
              <td>2 L</td>
              <td><span class="badge badge-success">In Stock</span></td>
              <td>
                <div class="btn-group btn-group-sm">
                  <button type="button" class="btn btn-outline-success btn-sm" title="Edit">
                    <i class="fas fa-edit"></i>
                  </button>
                  <button type="button" class="btn btn-outline-danger btn-sm" title="Delete">
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="text-center mt-3">
        <a href="#" class="btn btn-sm btn-success">View All Inventory</a>
      </div>
    {% else %}
      <div class="text-center py-4">
        <i class="fas fa-boxes fa-3x text-gray-300 mb-3"></i>
        <p>Select a farm to view inventory</p>
      </div>
    {% endif %}
  </div>
</div>
