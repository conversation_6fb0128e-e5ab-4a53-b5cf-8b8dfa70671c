{% extends "layout.html" %}
{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Add Member to {{ farm.name }}</h1>
        <div>
            <a href="{{ url_for('commercial.farm_detail', farm_id=farm.id) }}" class="d-none d-sm-inline-block btn btn-sm btn-outline-secondary shadow-sm">
                <i class="fas fa-arrow-left fa-sm mr-1"></i> Back to Farm
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">Add New Member</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="">
                        {{ form.hidden_tag() }}
                        <div class="form-group">
                            {{ form.email.label(class="form-control-label") }}
                            {% if form.email.errors %}
                                {{ form.email(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.email.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.email(class="form-control") }}
                            {% endif %}
                            <small class="form-text text-muted">Enter the email address of the user you want to add to this farm.</small>
                        </div>
                        
                        <div class="form-group">
                            {{ form.role.label(class="form-control-label") }}
                            {% if form.role.errors %}
                                {{ form.role(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.role.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.role(class="form-control") }}
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            {{ form.submit(class="btn btn-success") }}
                            <a href="{{ url_for('commercial.farm_detail', farm_id=farm.id) }}" class="btn btn-outline-secondary">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">Member Roles</h6>
                </div>
                <div class="card-body">
                    <p>Choose the appropriate role for the new member:</p>
                    
                    <div class="mb-3">
                        <h6 class="font-weight-bold">Admin</h6>
                        <p>Admins can:</p>
                        <ul>
                            <li>Manage farm settings</li>
                            <li>Add/remove members</li>
                            <li>Manage inventory and sales</li>
                            <li>Access all farm data</li>
                        </ul>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="font-weight-bold">Gardener</h6>
                        <p>Gardeners can:</p>
                        <ul>
                            <li>Update plant status</li>
                            <li>Record harvests</li>
                            <li>Create and manage events</li>
                            <li>View inventory and sales data</li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle mr-1"></i> The user must already have an account in the system to be added as a member.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
