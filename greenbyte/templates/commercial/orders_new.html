{% extends "layout.html" %}
{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Order Management</h1>
        <a href="#" class="d-none d-sm-inline-block btn btn-sm btn-success shadow-sm" data-toggle="modal" data-target="#addOrderModal">
            <i class="fas fa-plus fa-sm text-white-50 mr-1"></i> Create New Order
        </a>
    </div>

    <!-- Search and Filter Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-success">Search Orders</h6>
            <div class="dropdown no-arrow">
                <a class="dropdown-toggle" href="#" role="button" id="filterDropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="fas fa-filter fa-sm fa-fw text-gray-400"></i>
                </a>
                <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="filterDropdown">
                    <div class="dropdown-header">Filter By Status:</div>
                    <a class="dropdown-item" href="#">All Orders</a>
                    <a class="dropdown-item" href="#">Pending</a>
                    <a class="dropdown-item" href="#">Processing</a>
                    <a class="dropdown-item" href="#">In Transit</a>
                    <a class="dropdown-item" href="#">Delivered</a>
                    <a class="dropdown-item" href="#">Cancelled</a>
                    <div class="dropdown-divider"></div>
                    <div class="dropdown-header">Filter By Payment:</div>
                    <a class="dropdown-item" href="#">Paid</a>
                    <a class="dropdown-item" href="#">Pending</a>
                    <a class="dropdown-item" href="#">Overdue</a>
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item" href="#">Clear Filters</a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search orders by ID, client, or product..." id="orderSearch">
                        <div class="input-group-append">
                            <button class="btn btn-success" type="button">
                                <i class="fas fa-search fa-sm"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-control" id="orderStatus">
                        <option value="all">All Statuses</option>
                        <option value="pending">Pending</option>
                        <option value="processing">Processing</option>
                        <option value="transit">In Transit</option>
                        <option value="delivered">Delivered</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-control" id="orderSort">
                        <option value="date-desc">Newest First</option>
                        <option value="date-asc">Oldest First</option>
                        <option value="amount-desc">Amount: High to Low</option>
                        <option value="amount-asc">Amount: Low to High</option>
                    </select>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-row">
                        <div class="col-md-6">
                            <div class="form-group mb-0">
                                <label for="startDate" class="small mb-1">From Date</label>
                                <input type="date" class="form-control" id="startDate">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-0">
                                <label for="endDate" class="small mb-1">To Date</label>
                                <input type="date" class="form-control" id="endDate">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 d-flex align-items-end justify-content-end">
                    <button class="btn btn-primary" id="applyFilters">
                        <i class="fas fa-filter fa-sm mr-1"></i> Apply Filters
                    </button>
                    <button class="btn btn-secondary ml-2" id="clearFilters">
                        <i class="fas fa-undo fa-sm mr-1"></i> Clear
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Orders Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-success">Order List</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table" id="ordersTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Order ID</th>
                            <th>Client</th>
                            <th>Order Date</th>
                            <th>Delivery Date</th>
                            <th>Total Amount</th>
                            <th>Status</th>
                            <th>Payment</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="order-row" data-order-id="1">
                            <td>ORD-2023-001</td>
                            <td><a href="#" class="order-client-link">Farm Fresh Restaurant</a></td>
                            <td>Apr 15, 2023</td>
                            <td>Apr 18, 2023</td>
                            <td>$345.00</td>
                            <td>
                                <span class="badge badge-success status-badge" onclick="cycleOrderStatus(this, 1)" style="cursor: pointer;">Delivered</span>
                            </td>
                            <td>
                                <span class="badge badge-success payment-badge" onclick="cyclePaymentStatus(this, 1)" style="cursor: pointer;">Paid</span>
                            </td>
                        </tr>
                        <tr class="order-row" data-order-id="2">
                            <td>ORD-2023-002</td>
                            <td><a href="#" class="order-client-link">Green Leaf Cafe</a></td>
                            <td>Apr 14, 2023</td>
                            <td>Apr 17, 2023</td>
                            <td>$278.50</td>
                            <td>
                                <span class="badge badge-warning status-badge" onclick="cycleOrderStatus(this, 2)" style="cursor: pointer;">In Transit</span>
                            </td>
                            <td>
                                <span class="badge badge-warning payment-badge" onclick="cyclePaymentStatus(this, 2)" style="cursor: pointer;">Pending</span>
                            </td>
                        </tr>
                        <tr class="order-row" data-order-id="3">
                            <td>ORD-2023-003</td>
                            <td><a href="#" class="order-client-link">Harvest Market</a></td>
                            <td>Apr 13, 2023</td>
                            <td>Apr 16, 2023</td>
                            <td>$512.75</td>
                            <td>
                                <span class="badge badge-info status-badge" onclick="cycleOrderStatus(this, 3)" style="cursor: pointer;">Processing</span>
                            </td>
                            <td>
                                <span class="badge badge-warning payment-badge" onclick="cyclePaymentStatus(this, 3)" style="cursor: pointer;">Pending</span>
                            </td>
                        </tr>
                        <tr class="order-row" data-order-id="4">
                            <td>ORD-2023-004</td>
                            <td><a href="#" class="order-client-link">John Smith</a></td>
                            <td>Apr 12, 2023</td>
                            <td>Apr 15, 2023</td>
                            <td>$87.25</td>
                            <td>
                                <span class="badge badge-success status-badge" onclick="cycleOrderStatus(this, 4)" style="cursor: pointer;">Delivered</span>
                            </td>
                            <td>
                                <span class="badge badge-success payment-badge" onclick="cyclePaymentStatus(this, 4)" style="cursor: pointer;">Paid</span>
                            </td>
                        </tr>
                        <tr class="order-row" data-order-id="5">
                            <td>ORD-2023-005</td>
                            <td><a href="#" class="order-client-link">Organic Delights</a></td>
                            <td>Apr 11, 2023</td>
                            <td>Apr 14, 2023</td>
                            <td>$156.50</td>
                            <td>
                                <span class="badge badge-danger status-badge" onclick="cycleOrderStatus(this, 5)" style="cursor: pointer;">Cancelled</span>
                            </td>
                            <td>
                                <span class="badge badge-danger payment-badge" onclick="cyclePaymentStatus(this, 5)" style="cursor: pointer;">Overdue</span>
                            </td>
                        </tr>
                        <tr class="order-row" data-order-id="6">
                            <td>ORD-2023-006</td>
                            <td><a href="#" class="order-client-link">Fresh & Local Co-op</a></td>
                            <td>Apr 10, 2023</td>
                            <td>Apr 13, 2023</td>
                            <td>$325.00</td>
                            <td>
                                <span class="badge badge-success status-badge" onclick="cycleOrderStatus(this, 6)" style="cursor: pointer;">Delivered</span>
                            </td>
                            <td>
                                <span class="badge badge-success payment-badge" onclick="cyclePaymentStatus(this, 6)" style="cursor: pointer;">Paid</span>
                            </td>
                        </tr>
                        <tr class="order-row" data-order-id="7">
                            <td>ORD-2023-007</td>
                            <td><a href="#" class="order-client-link">Garden Bistro</a></td>
                            <td>Apr 09, 2023</td>
                            <td>Apr 12, 2023</td>
                            <td>$245.75</td>
                            <td>
                                <span class="badge badge-success status-badge" onclick="cycleOrderStatus(this, 7)" style="cursor: pointer;">Delivered</span>
                            </td>
                            <td>
                                <span class="badge badge-danger payment-badge" onclick="cyclePaymentStatus(this, 7)" style="cursor: pointer;">Overdue</span>
                            </td>
                        </tr>
                        <tr class="order-row" data-order-id="8">
                            <td>ORD-2023-008</td>
                            <td><a href="#" class="order-client-link">Sunshine Grocers</a></td>
                            <td>Apr 08, 2023</td>
                            <td>Apr 11, 2023</td>
                            <td>$178.25</td>
                            <td>
                                <span class="badge badge-secondary status-badge" onclick="cycleOrderStatus(this, 8)" style="cursor: pointer;">Pending</span>
                            </td>
                            <td>
                                <span class="badge badge-warning payment-badge" onclick="cyclePaymentStatus(this, 8)" style="cursor: pointer;">Pending</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add Order Modal -->
<div class="modal fade" id="addOrderModal" tabindex="-1" role="dialog" aria-labelledby="addOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addOrderModalLabel">Create New Order</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form>
                    <!-- Client Selection -->
                    <div class="form-group">
                        <label for="clientSelect">Client</label>
                        <select class="form-control" id="clientSelect">
                            <option value="">Select a client...</option>
                            <option value="1">Farm Fresh Restaurant</option>
                            <option value="2">Green Leaf Cafe</option>
                            <option value="3">Harvest Market</option>
                            <option value="4">John Smith</option>
                            <option value="5">Organic Delights</option>
                            <option value="6">Fresh & Local Co-op</option>
                            <option value="7">Garden Bistro</option>
                            <option value="8">Sunshine Grocers</option>
                        </select>
                    </div>
                    
                    <!-- Delivery Date -->
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="deliveryDate">Delivery Date</label>
                            <input type="date" class="form-control" id="deliveryDate">
                        </div>
                        <div class="form-group col-md-6">
                            <label for="paymentTerms">Payment Terms</label>
                            <select class="form-control" id="paymentTerms">
                                <option value="immediate">Immediate Payment</option>
                                <option value="7days">Net 7 Days</option>
                                <option value="14days">Net 14 Days</option>
                                <option value="30days">Net 30 Days</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- Product Selection -->
                    <h6 class="font-weight-bold mt-4 mb-3">Order Items</h6>
                    <div id="orderItems">
                        <div class="order-item mb-3 pb-3 border-bottom">
                            <div class="form-row">
                                <div class="col-md-5">
                                    <label>Product</label>
                                    <select class="form-control product-select">
                                        <option value="">Select a product...</option>
                                        <option value="1">Tomatoes (Roma) - $3.99/lb</option>
                                        <option value="2">Lettuce (Romaine) - $2.49/head</option>
                                        <option value="3">Basil (Sweet) - $2.99/bunch</option>
                                        <option value="4">Carrots (Nantes) - $1.99/lb</option>
                                        <option value="5">Bell Peppers (Red) - $3.49/lb</option>
                                        <option value="6">Strawberries (Organic) - $4.99/pint</option>
                                        <option value="7">Cilantro - $1.49/bunch</option>
                                        <option value="8">Apples (Honeycrisp) - $2.99/lb</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label>Quantity</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control product-quantity" min="1" value="1">
                                        <div class="input-group-append">
                                            <span class="input-group-text">units</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label>Price</label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="text" class="form-control product-price" readonly value="0.00">
                                    </div>
                                </div>
                                <div class="col-md-1 d-flex align-items-end">
                                    <button type="button" class="btn btn-danger remove-item" disabled>
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <button type="button" class="btn btn-success btn-sm" id="addItemBtn">
                        <i class="fas fa-plus fa-sm mr-1"></i> Add Another Item
                    </button>
                    
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="specialInstructions">Special Instructions</label>
                                <textarea class="form-control" id="specialInstructions" rows="3" placeholder="Enter any special instructions or notes for this order..."></textarea>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="font-weight-bold mb-3">Order Summary</h6>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Subtotal:</span>
                                        <span id="subtotal">$0.00</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Tax (8%):</span>
                                        <span id="tax">$0.00</span>
                                    </div>
                                    <div class="d-flex justify-content-between font-weight-bold">
                                        <span>Total:</span>
                                        <span id="total">$0.00</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success">Create Order</button>
            </div>
        </div>
    </div>
</div>

<!-- CSS for badges and modern table -->
<style>
    /* Modern table styles */
    #ordersTable {
        width: 100%;
        border: none;
        font-size: 0.9rem;
    }
    
    #ordersTable th {
        background-color: #f8f9fc;
        color: #5a5c69;
        font-weight: 600;
        font-size: 0.85rem;
        border-top: none;
        border-bottom: 1px solid #e3e6f0;
        padding: 0.75rem 1rem;
    }
    
    #ordersTable td {
        padding: 0.75rem 1rem;
        vertical-align: middle;
        border-top: none;
        border-bottom: 1px solid #f1f1f5;
    }
    
    #ordersTable tbody tr:last-child td {
        border-bottom: none;
    }
    
    .order-row {
        cursor: pointer;
        transition: all 0.15s;
    }
    
    .order-row:hover {
        background-color: rgba(28, 200, 138, 0.03);
    }
    
    /* Badge styles */
    .badge {
        padding: 0.4em 0.65em;
        font-weight: 500;
        border-radius: 3px;
        font-size: 0.75rem;
    }
    
    .badge-success {
        background-color: #1cc88a;
        color: white;
    }
    
    .badge-warning {
        background-color: #f6c23e;
        color: white;
    }
    
    .badge-info {
        background-color: #36b9cc;
        color: white;
    }
    
    .badge-danger {
        background-color: #e74a3b;
        color: white;
    }
    
    .badge-secondary {
        background-color: #858796;
        color: white;
    }
    
    /* Client name link styling */
    .order-client-link {
        color: #1cc88a;
        font-weight: 600;
        text-decoration: none;
        transition: color 0.2s;
    }
    
    .order-client-link:hover {
        color: #169b6b;
        text-decoration: none;
    }
    
    /* Remove table borders */
    .table {
        border: none;
    }
    
    .table td, .table th {
        border-left: none;
        border-right: none;
    }
    
    /* Adjust table header */
    .table thead th {
        border-bottom: 1px solid #e3e6f0;
    }
</style>

{% endblock content %}

{% block scripts %}
<script>
    // Function to cycle through order statuses when clicking on a badge
    function cycleOrderStatus(badge, orderId) {
        // Get the current text
        var currentText = badge.innerText.trim();
        var newClass, newText;
        
        // Determine the next status in the cycle
        if (currentText === "Pending") {
            newClass = "badge-info";
            newText = "Processing";
        } else if (currentText === "Processing") {
            newClass = "badge-warning";
            newText = "In Transit";
        } else if (currentText === "In Transit") {
            newClass = "badge-success";
            newText = "Delivered";
        } else if (currentText === "Delivered") {
            newClass = "badge-danger";
            newText = "Cancelled";
        } else {
            newClass = "badge-secondary";
            newText = "Pending";
        }
        
        // Remove all badge classes
        badge.classList.remove("badge-secondary", "badge-info", "badge-warning", "badge-success", "badge-danger");
        
        // Add the new class and text
        badge.classList.add(newClass);
        badge.innerText = newText;
        
        // In a real application, you would send an AJAX request to update the status in the database
        console.log("Order status changed to: " + newText + " for order ID: " + orderId);
    }
    
    // Function to cycle through payment statuses when clicking on a badge
    function cyclePaymentStatus(badge, orderId) {
        // Get the current text
        var currentText = badge.innerText.trim();
        var newClass, newText;
        
        // Determine the next status in the cycle
        if (currentText === "Pending") {
            newClass = "badge-success";
            newText = "Paid";
        } else if (currentText === "Paid") {
            newClass = "badge-danger";
            newText = "Overdue";
        } else {
            newClass = "badge-warning";
            newText = "Pending";
        }
        
        // Remove all badge classes
        badge.classList.remove("badge-warning", "badge-success", "badge-danger");
        
        // Add the new class and text
        badge.classList.add(newClass);
        badge.innerText = newText;
        
        // In a real application, you would send an AJAX request to update the status in the database
        console.log("Payment status changed to: " + newText + " for order ID: " + orderId);
    }

    $(document).ready(function() {
        // Initialize DataTable
        $('#ordersTable').DataTable({
            "pageLength": 10,
            "ordering": true,
            "info": true,
            "searching": true
        });
        
        // Order search functionality
        $('#orderSearch').on('keyup', function() {
            var value = $(this).val().toLowerCase();
            $('#ordersTable tbody tr').filter(function() {
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
            });
        });
        
        // Order status filter
        $('#orderStatus').change(function() {
            var status = $(this).val().toLowerCase();
            if (status === 'all') {
                $('#ordersTable tbody tr').show();
            } else {
                $('#ordersTable tbody tr').each(function() {
                    var rowStatus = $(this).find('td:nth-child(6) span').text().toLowerCase();
                    $(this).toggle(rowStatus.indexOf(status) > -1);
                });
            }
        });
        
        // Date range filter
        $('#applyFilters').click(function() {
            var startDate = $('#startDate').val();
            var endDate = $('#endDate').val();
            
            if (startDate && endDate) {
                startDate = new Date(startDate);
                endDate = new Date(endDate);
                
                $('#ordersTable tbody tr').each(function() {
                    var orderDateStr = $(this).find('td:nth-child(3)').text();
                    var orderDate = new Date(orderDateStr);
                    
                    $(this).toggle(orderDate >= startDate && orderDate <= endDate);
                });
            }
        });
        
        // Clear filters
        $('#clearFilters').click(function() {
            $('#orderSearch').val('');
            $('#orderStatus').val('all');
            $('#startDate').val('');
            $('#endDate').val('');
            $('#ordersTable tbody tr').show();
        });
        
        // Add item button
        $('#addItemBtn').click(function() {
            var newItem = $('.order-item:first').clone();
            newItem.find('input').val('');
            newItem.find('select').val('');
            newItem.find('.remove-item').prop('disabled', false);
            $('#orderItems').append(newItem);
        });
        
        // Remove item button
        $(document).on('click', '.remove-item', function() {
            $(this).closest('.order-item').remove();
            updateOrderSummary();
        });
        
        // Update price when product or quantity changes
        $(document).on('change', '.product-select, .product-quantity', function() {
            var row = $(this).closest('.order-item');
            var product = row.find('.product-select').val();
            var quantity = row.find('.product-quantity').val();
            
            if (product && quantity) {
                // In a real app, you would fetch the actual price from the database
                // This is just a simplified example
                var price = 0;
                switch(product) {
                    case '1': price = 3.99; break;
                    case '2': price = 2.49; break;
                    case '3': price = 2.99; break;
                    case '4': price = 1.99; break;
                    case '5': price = 3.49; break;
                    case '6': price = 4.99; break;
                    case '7': price = 1.49; break;
                    case '8': price = 2.99; break;
                }
                
                var total = price * quantity;
                row.find('.product-price').val(total.toFixed(2));
                
                updateOrderSummary();
            }
        });
        
        // Update order summary
        function updateOrderSummary() {
            var subtotal = 0;
            
            $('.product-price').each(function() {
                var price = parseFloat($(this).val()) || 0;
                subtotal += price;
            });
            
            var tax = subtotal * 0.08;
            var total = subtotal + tax;
            
            $('#subtotal').text('$' + subtotal.toFixed(2));
            $('#tax').text('$' + tax.toFixed(2));
            $('#total').text('$' + total.toFixed(2));
        }
        
        // Order row click handler
        $('.order-row').click(function(e) {
            // Only navigate if the click wasn't on a button, link, or badge
            if (!$(e.target).is('button, a, input, .status-badge, .payment-badge')) {
                var orderId = $(this).data('order-id');
                // This would navigate to the order detail page
                // window.location.href = '/commercial/orders/' + orderId;
                alert('Navigate to order details for ID: ' + orderId);
            }
        });
    });
</script>
{% endblock scripts %}
