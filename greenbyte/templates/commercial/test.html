{% extends "layout.html" %}
{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">jQuery Test</h1>
    
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-success">Click Test</h6>
        </div>
        <div class="card-body">
            <p>Click the buttons below to test jQuery:</p>
            
            <button id="testButton" class="btn btn-primary mb-3">Click me</button>
            <div id="testResult" class="alert alert-info" style="display: none;">Button clicked!</div>
            
            <hr>
            
            <div class="mt-3">
                <span id="statusBadge" class="badge badge-success" data-status="active">Active</span>
                <div id="statusResult" class="mt-2"></div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Simple button click test
        $("#testButton").click(function() {
            $("#testResult").show();
        });
        
        // Status badge click test
        $("#statusBadge").click(function() {
            var currentStatus = $(this).data("status");
            var newStatus;
            
            // Determine next status
            if (currentStatus === "active") {
                newStatus = "low";
                $(this).removeClass("badge-success").addClass("badge-warning").text("Low Stock");
            } else if (currentStatus === "low") {
                newStatus = "out";
                $(this).removeClass("badge-warning").addClass("badge-danger").text("Out of Stock");
            } else {
                newStatus = "active";
                $(this).removeClass("badge-danger").addClass("badge-success").text("Active");
            }
            
            // Update data attribute
            $(this).data("status", newStatus);
            
            // Show result
            $("#statusResult").html("Status changed to: <strong>" + $(this).text() + "</strong>");
        });
    });
</script>
{% endblock %}
