{% extends "layout.html" %} {% block content %}
{% block styles %}
<style>
  /* Top Commercial Navbar Styles */
  .custom-navbar {
    padding: 0.5rem 1rem;
    border-radius: 1.5rem !important;
    overflow: hidden;
    border: 1px solid rgba(28, 200, 138, 0.2) !important;
    border-bottom: 4px solid rgba(28, 200, 138, 0.2) !important;
    background: white !important;
    box-shadow: 0 8px 24px rgba(0,0,0,0.12) !important;
  }

  .navbar-brand {
    font-size: 1.1rem;
    padding: 0;
    color: #1cc88a;
  }

  .nav-item {
    position: relative;
  }

  .nav-link {
    padding: 0.5rem 1rem;
    color: #5a5c69;
    transition: all 0.3s ease;
    text-decoration: none;
  }

  .nav-link:hover {
    color: #1cc88a;
  }

  /* Full-width nav items */
  #commercialNavbar .navbar-nav {
    width: 100%;
  }

  #commercialNavbar .nav-item {
    width: 100%;
    margin-bottom: 2px;
  }

  #commercialNavbar .nav-link {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease-in-out;
    font-size: 0.9rem;
  }

  #commercialNavbar .nav-item.active .nav-link {
    color: #1cc88a;
    font-weight: 600;
    background-color: rgba(28, 200, 138, 0.1);
  }

  #commercialNavbar .nav-link:hover {
    background-color: rgba(28, 200, 138, 0.05);
  }

  /* Custom card styling */
  .custom-card {
    border: 1px solid rgba(28, 200, 138, 0.2) !important;
    border-radius: 1.5rem !important;
    border-bottom: 4px solid rgba(28, 200, 138, 0.2) !important;
    background: white !important;
    box-shadow: 0 8px 24px rgba(0,0,0,0.12) !important;
    overflow: hidden;
  }

  /* Card styling */
  .card {
    border-radius: 1.5rem !important;
    border: 1px solid rgba(28, 200, 138, 0.2) !important;
    border-bottom: 4px solid rgba(28, 200, 138, 0.2) !important;
    overflow: hidden;
  }

  .card-header {
    background-color: rgba(28, 200, 138, 0.05) !important;
    border-bottom: 1px solid rgba(28, 200, 138, 0.1) !important;
  }

  /* Custom rounded card styling */
  .rounded-custom {
    border-radius: 1.5rem !important;
  }

  .rounded-custom .card-header {
    border-top-left-radius: 1.5rem !important;
    border-top-right-radius: 1.5rem !important;
  }

  .rounded-custom .card-body:last-child {
    border-bottom-left-radius: 1.5rem !important;
    border-bottom-right-radius: 1.5rem !important;
  }

  /* Farm details styling */
  .stat-item {
    margin-bottom: 0.5rem;
  }

  .stat-label {
    font-weight: 600;
    color: #5a5c69;
    margin-right: 0.5rem;
  }

  .stat-value {
    font-weight: 700;
    color: #1cc88a;
  }

  /* Farm members styling */
  .icon-circle {
    height: 2.5rem;
    width: 2.5rem;
    border-radius: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  .list-group-item {
    border: 1px solid rgba(28, 200, 138, 0.1);
    margin-bottom: 0.5rem;
    border-radius: 0.5rem !important;
    transition: all 0.2s;
  }

  .list-group-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.05);
    border-color: rgba(28, 200, 138, 0.2);
  }

  .badge-success {
    background-color: #1cc88a;
    color: white;
  }

  .badge-light {
    background-color: #f8f9fc;
    color: #5a5c69;
    border: 1px solid rgba(28, 200, 138, 0.1);
  }

  /* Activity log styling */
  .activity-icon {
    height: 2rem;
    width: 2rem;
    border-radius: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  .bg-info {
    background-color: #36b9cc !important;
  }

  .bg-warning {
    background-color: #f6c23e !important;
  }

  .bg-danger {
    background-color: #e74a3b !important;
  }

  .badge {
    padding: 0.4em 0.65em;
    font-weight: 500;
    border-radius: 0.25rem;
  }

  .badge-info {
    background-color: #36b9cc;
    color: white;
  }

  .badge-warning {
    background-color: #f6c23e;
    color: white;
  }

  .badge-danger {
    background-color: #e74a3b;
    color: white;
  }

  .table {
    margin-bottom: 0;
  }

  .table th {
    border-top: none;
    border-bottom: 1px solid rgba(28, 200, 138, 0.1);
    color: #5a5c69;
    font-weight: 600;
    padding: 0.75rem 1rem;
  }

  .table td {
    vertical-align: middle;
    padding: 0.75rem 1rem;
    border-top: 1px solid rgba(28, 200, 138, 0.05);
  }

  .table-hover tbody tr:hover {
    background-color: rgba(28, 200, 138, 0.03);
  }
</style>
{% endblock %}
<div class="container-fluid">
  <div class="row">
    <!-- Left Column - Main Content -->
    <div class="col-lg-8">
      <!-- Top Commercial Navigation Bar -->
      <div class="mb-4">
        <nav class="navbar navbar-expand-lg navbar-light bg-white custom-navbar custom-card shadow py-2 px-3">
          <a class="navbar-brand text-success py-0" href="#" data-target="dashboard-card" style="font-size: 1.1rem;">
            <i class="fas fa-seedling mr-2"></i> Farm Hub Dashboard
          </a>
          <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#commercialNavbar" aria-controls="commercialNavbar" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
          </button>
          <div class="collapse navbar-collapse" id="commercialNavbar">
            <ul class="navbar-nav w-100">
              <li class="nav-item">
                <a class="nav-link" href="#" data-target="financial-card">
                  <div class="d-flex align-items-center">
                    <i class="fas fa-chart-line mr-2 text-success fa-sm"></i>
                    <span>Financials</span>
                  </div>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="#" data-target="inventory-card">
                  <div class="d-flex align-items-center">
                    <i class="fas fa-boxes mr-2 text-success fa-sm"></i>
                    <span>Inventory</span>
                  </div>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="#" data-target="orders-card">
                  <div class="d-flex align-items-center">
                    <i class="fas fa-shopping-cart mr-2 text-success fa-sm"></i>
                    <span>Orders</span>
                  </div>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="#" data-target="clients-card">
                  <div class="d-flex align-items-center">
                    <i class="fas fa-users mr-2 text-success fa-sm"></i>
                    <span>Clients</span>
                  </div>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="#" data-target="activities-card">
                  <div class="d-flex align-items-center">
                    <i class="fas fa-history mr-2 text-success fa-sm"></i>
                    <span>Activities</span>
                  </div>
                </a>
              </li>
            </ul>
          </div>
        </nav>
      </div>

      <!-- Main Content Area -->
      <!-- Dashboard Card -->
      <div id="dashboard-card" class="content-card d-none">
        <div class="card shadow mb-4 rounded-custom">
          <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-success">Farm Hub Dashboard</h6>
            <div class="dropdown no-arrow">
              <a class="dropdown-toggle" href="#" role="button" id="dashboardDropdownMenu" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
              </a>
              <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dashboardDropdownMenu">
                <div class="dropdown-header">Actions:</div>
                <a class="dropdown-item" href="#"><i class="fas fa-sync-alt fa-sm fa-fw mr-2 text-success"></i>Refresh Data</a>
                <a class="dropdown-item" href="#"><i class="fas fa-cog fa-sm fa-fw mr-2 text-success"></i>Settings</a>
              </div>
            </div>
          </div>
          <div class="card-body">
            <!-- Quick Stats Row -->
            <div class="row mb-4">
              <div class="col-md-3 mb-3">
                <div class="card border-left-success shadow h-100 py-2 rounded-custom">
                  <div class="card-body">
                    <div class="row no-gutters align-items-center">
                      <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Gardens</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ selected_farm.gardens|length if selected_farm and selected_farm.gardens else 0 }}</div>
                      </div>
                      <div class="col-auto">
                        <i class="fas fa-leaf fa-2x text-gray-300"></i>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-3 mb-3">
                <div class="card border-left-primary shadow h-100 py-2 rounded-custom">
                  <div class="card-body">
                    <div class="row no-gutters align-items-center">
                      <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Plants</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">124</div>
                      </div>
                      <div class="col-auto">
                        <i class="fas fa-seedling fa-2x text-gray-300"></i>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-3 mb-3">
                <div class="card border-left-info shadow h-100 py-2 rounded-custom">
                  <div class="card-body">
                    <div class="row no-gutters align-items-center">
                      <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Members</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ (selected_farm.members|length) + 1 if selected_farm and selected_farm.members else 1 }}</div>
                      </div>
                      <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-3 mb-3">
                <div class="card border-left-warning shadow h-100 py-2 rounded-custom">
                  <div class="card-body">
                    <div class="row no-gutters align-items-center">
                      <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Tasks</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">7</div>
                      </div>
                      <div class="col-auto">
                        <i class="fas fa-tasks fa-2x text-gray-300"></i>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Quick Links -->
            <div class="row mb-4">
              <div class="col-lg-12">
                <h6 class="font-weight-bold mb-3">Quick Actions</h6>
                <div class="row">
                  <div class="col-md-3 mb-3">
                    <a href="#" class="btn btn-success btn-block py-3" data-target="inventory-card" onclick="document.querySelector('[data-target=\'inventory-card\']').click()">
                      <i class="fas fa-boxes mr-2"></i> Manage Inventory
                    </a>
                  </div>
                  <div class="col-md-3 mb-3">
                    <a href="#" class="btn btn-primary btn-block py-3" data-target="orders-card" onclick="document.querySelector('[data-target=\'orders-card\']').click()">
                      <i class="fas fa-shopping-cart mr-2"></i> View Orders
                    </a>
                  </div>
                  <div class="col-md-3 mb-3">
                    <a href="#" class="btn btn-info btn-block py-3" data-target="clients-card" onclick="document.querySelector('[data-target=\'clients-card\']').click()">
                      <i class="fas fa-users mr-2"></i> Manage Clients
                    </a>
                  </div>
                  <div class="col-md-3 mb-3">
                    <a href="#" class="btn btn-warning btn-block py-3" data-target="financial-card" onclick="document.querySelector('[data-target=\'financial-card\']').click()">
                      <i class="fas fa-chart-line mr-2"></i> View Financials
                    </a>
                  </div>
                </div>
              </div>
            </div>

            <!-- Recent Activity -->
            <div class="row">
              <div class="col-lg-12">
                <h6 class="font-weight-bold mb-3">Recent Activity</h6>
                <div class="table-responsive">
                  <table class="table table-hover">
                    <tbody>
                      <tr>
                        <td width="15%"><span class="text-muted">Today, 10:23 AM</span></td>
                        <td width="15%">
                          <div class="d-flex align-items-center">
                            <div class="activity-icon bg-success mr-2" style="width: 24px; height: 24px;">
                              <i class="fas fa-user text-white small"></i>
                            </div>
                            <span>John Doe</span>
                          </div>
                        </td>
                        <td><span class="badge badge-success">Added</span> Plant</td>
                        <td>Added <a href="#">Tomato (Roma)</a> to <a href="#">Vegetable Zone</a></td>
                      </tr>
                      <tr>
                        <td><span class="text-muted">Today, 9:45 AM</span></td>
                        <td>
                          <div class="d-flex align-items-center">
                            <div class="activity-icon bg-info mr-2" style="width: 24px; height: 24px;">
                              <i class="fas fa-user text-white small"></i>
                            </div>
                            <span>Sarah Smith</span>
                          </div>
                        </td>
                        <td><span class="badge badge-info">Updated</span> Zone</td>
                        <td>Changed status of <a href="#">Herb Garden</a> to <span class="text-success">Active</span></td>
                      </tr>
                      <tr>
                        <td><span class="text-muted">Yesterday</span></td>
                        <td>
                          <div class="d-flex align-items-center">
                            <div class="activity-icon bg-warning mr-2" style="width: 24px; height: 24px;">
                              <i class="fas fa-user text-white small"></i>
                            </div>
                            <span>Mike Johnson</span>
                          </div>
                        </td>
                        <td><span class="badge badge-warning">Modified</span> Garden</td>
                        <td>Updated irrigation schedule for <a href="#">Main Garden</a></td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <div class="text-center mt-3">
                  <a href="#" class="btn btn-sm btn-success" data-target="activities-card" onclick="document.querySelector('[data-target=\'activities-card\']').click()">
                    View All Activities
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Financial Summary Card -->
      <div id="financial-card" class="content-card d-none">
        {% include 'commercial/components/financial_summary_card.html' %}
      </div>

      <!-- Inventory Card -->
      <div id="inventory-card" class="content-card d-none">
        {% include 'commercial/components/inventory_card.html' %}
      </div>

      <!-- Orders Card -->
      <div id="orders-card" class="content-card d-none">
        {% include 'commercial/components/orders_card.html' %}
      </div>

      <!-- Clients Card -->
      <div id="clients-card" class="content-card d-none">
        {% include 'commercial/components/clients_card.html' %}
      </div>

      <!-- Member Activities Card -->
      <div id="activities-card" class="content-card d-none">
        {% include 'commercial/components/member_activities_card.html' %}
      </div>
    </div>

    <!-- Right Column - Additional Cards -->
    <div class="col-lg-4">
      <!-- Farm Details Card -->
      {% include 'commercial/components/farm_details_card.html' %}

      <!-- Farm Members Card -->
      {% include 'commercial/components/farm_members_card.html' %}
    </div>
  </div>
</div>
{% endblock content %}



{% block scripts %}
<script src="{{ url_for('static', filename='js/farm_hub.js') }}"></script>
<script>
  $(document).ready(function() {
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
  });
</script>
{% endblock %}
