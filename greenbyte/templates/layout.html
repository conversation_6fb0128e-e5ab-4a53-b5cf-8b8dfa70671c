<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="csrf-token" content="{{ csrf_token() }}">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
  <link href="{{ url_for('static', filename='css/custom.css') }}" rel="stylesheet">
  <link href="{{ url_for('static', filename='css/calendar.css') }}" rel="stylesheet">
  <link href="{{ url_for('static', filename='css/calendar-custom.css') }}" rel="stylesheet">
  <link href="{{ url_for('static', filename='css/layout.css') }}" rel="stylesheet">

  <style>
    /* Hide dropdown arrow for garden nav item */
    #gardenDropdown::after {
      display: none !important;
    }
  </style>

  <title>GreenByte Dashboard</title>
</head>

<body class="">
  <!-- Mobile Hamburger Menu Button -->
  <button id="sidebarToggle" class="hamburger-menu">
    <i class="fas fa-bars"></i>
  </button>

  <!-- Sidebar Overlay -->
  <div id="sidebar-overlay"></div>

  <!-- Floating Sidebar -->
  <nav class="sidebar">
    <!-- Logo Section -->
    <div class="logo-section">
      <i class="fas fa-seedling"></i>
    </div>

    <!-- Navigation Section -->
    <div class="nav-section">
      <div class="nav-item">
        <a class="nav-link" href="{{ url_for('main.index') }}">
          <i class="fas fa-fw fa-home"></i>
          <span>Home</span>
        </a>
      </div>
       <div class="nav-item">
        <a class="nav-link" href="{{ url_for('main.calendar') }}">
          <i class="fas fa-fw fa-calendar"></i>
          <span>Schedule</span>
        </a>
      </div>
    <div class="pl-3 pr-3 py-2">
        <div class="commercial-divider"></div>
      </div>
      <div class="nav-item dropdown">
        <a class="nav-link dropdown-toggle" href="#" id="gardenDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" style="position: relative;">
          <i class="fas fa-fw fa-leaf"></i>
          <span>Garden</span>
        </a>
        <ul class="dropdown-menu" aria-labelledby="gardenDropdown">
          <li><a class="dropdown-item" href="{{ url_for('gardens.view_gardens') }}">All Gardens</a></li>
          {% if current_user.is_authenticated %}
            <li><hr class="dropdown-divider"></li>
            {% for garden in current_user.gardens %}
              <li><a class="dropdown-item" href="{{ url_for('gardens.view_garden', garden_id=garden.id) }}">{{ garden.name }}</a></li>
            {% endfor %}
            <li><hr class="dropdown-divider"></li>
            <li><a class="dropdown-item" href="{{ url_for('gardens.add_garden') }}">
              <i class="fas fa-plus me-2"></i>Add Garden
            </a></li>
          {% endif %}
        </ul>
      </div>

     

      <div class="nav-item">
        <a class="nav-link" href="{{ url_for('maple_syrup.index') }}">
          <i class="fas fa-fw fa-tint"></i>
          <span>Maple Syrup</span>
        </a>
      </div>

      <hr class="sidebar-divider">


      <hr class="sidebar-divider">

      <!-- Commercial Section -->
      <div class="pl-3 pr-3 py-2">
        <div class="commercial-divider"></div>
      </div>

      <div class="nav-item">
        <a class="nav-link" href="{{ url_for('commercial.farm_hub') }}">
          <i class="fas fa-fw fa-tractor"></i>
          <span>Farm</span>
        </a>
      </div>
      
      <div class="nav-item">
        <a class="nav-link" href="{{ url_for('main.analytics') }}">
          <i class="fas fa-fw fa-chart-line"></i>
          <span>Analytics</span>
        </a>
      </div>
      <hr class="sidebar-divider d-none d-md-block">
    </div>

    <!-- User Section -->
    <div class="user-section">
      {% if current_user.is_authenticated %}
      <!-- Logged in user dropdown -->
      <button id="userDropdownBtn" class="user-btn">
        <i class="fas fa-user-circle"></i>
      </button>

      <!-- Add Dropdown Menu -->
      <div id="userDropdown" class="user-dropdown">
        <a href="{{ url_for('users.page_user', username=current_user.username) }}" class="dropdown-item">
          <i class="fas fa-user fa-sm fa-fw mr-2"></i>
          Profile
        </a>

        <div class="dropdown-divider"></div>
        <a href="{{ url_for('users.logout') }}" class="dropdown-item">
          <i class="fas fa-sign-out-alt fa-sm fa-fw mr-2"></i>
          Logout
        </a>
      </div>
      {% else %}
      <!-- Not logged in - show login/register buttons -->
      <button id="authDropdownBtn" class="user-btn">
        <i class="fas fa-sign-in-alt"></i>
      </button>

      <!-- Auth Dropdown Menu -->
      <div id="authDropdown" class="user-dropdown">
        <a href="{{ url_for('users.login') }}" class="dropdown-item">
          <i class="fas fa-sign-in-alt fa-sm fa-fw mr-2"></i>
          Login
        </a>

        <div class="dropdown-divider"></div>
        <a href="{{ url_for('users.register') }}" class="dropdown-item">
          <i class="fas fa-user-plus fa-sm fa-fw mr-2"></i>
          Register
        </a>
      </div>
      {% endif %}
    </div>
  </nav>

  <div id="content-wrapper">
    <!-- Add Flash Messages Container -->
    {% with messages = get_flashed_messages(with_categories=true) %}
      {% if messages %}
        <div id="flash-messages" class="toast-container position-fixed top-0 end-0 p-3">
          {% for category, message in messages %}
            {% set bg_color = 'bg-success' if category == 'success' else 'bg-danger' if category == 'danger' else 'bg-warning' if category == 'warning' else 'bg-info' if category == 'info' else 'bg-secondary' %}
            {% set icon = 'check-circle' if category == 'success' else 'exclamation-circle' if category == 'danger' else 'exclamation-triangle' if category == 'warning' else 'info-circle' if category == 'info' else 'bell' %}
            <div class="toast align-items-center text-white {{ bg_color }} border-0 show mb-3" role="alert" aria-live="assertive" aria-atomic="true">
              <div class="d-flex">
                <div class="toast-body">
                  <i class="fas fa-{{ icon }} me-2"></i>
                  {{ message }}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
              </div>
            </div>
          {% endfor %}
        </div>
      {% endif %}
    {% endwith %}

    <div id="content">
      <div class="container-fluid">
        <div class="row">
          {% block content %}{% endblock %}
        </div>
      </div>

      <!-- Footer -->
      <footer class="sticky-footer">
        <div class="container">
          <div class="text-center">
            <span>Copyright &copy; GreenByte 2023</span>
          </div>
        </div>
      </footer>
    </div>
  </div>

  <!-- Optional JavaScript; choose one of the two! -->

  <!-- Option 1: Bootstrap Bundle with Popper -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

  <!-- Page-specific scripts -->
  {% block scripts %}{% endblock %}

  <!-- Include JavaScript files -->
  <script src="{{ url_for('static', filename='js/layout.js') }}"></script>
  <!-- Toast container for notifications -->
  <div id="toast-container" class="toast-container position-fixed top-0 end-0 p-3"></div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

  <!-- Toast container for notifications -->
  <div class="position-fixed top-0 end-0 p-3 share-toast-container">
    <div id="shareToast" class="toast share-toast" role="alert" aria-live="assertive" aria-atomic="true">
      <div class="toast-header">
        <i class="fas fa-link me-2"></i>
        <strong class="me-auto">Share Link</strong>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
      </div>
      <div class="toast-body">
        <div class="input-group">
          <input type="text" id="shareLink" class="form-control" readonly>
          <button class="btn btn-outline-success" type="button" onclick="copyShareLink()">
            <i class="fas fa-copy"></i>
          </button>
        </div>
        <small class="text-muted mt-2 d-block">Click the button to copy the link</small>
      </div>
    </div>
  </div>

  <script src="{{ url_for('static', filename='js/events.js') }}"></script>
  <script src="{{ url_for('static', filename='js/toast.js') }}"></script>
  <script src="{{ url_for('static', filename='js/social.js') }}"></script>
  <script src="{{ url_for('static', filename='js/sidebar.js') }}"></script>
  <script src="{{ url_for('static', filename='js/flash_messages.js') }}"></script>
</body>

</html>
