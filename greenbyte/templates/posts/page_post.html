{% extends "layout.html" %}
{% block content %}
<div class="container py-4">
  <div class="row justify-content-center">
    <div class="col-xl-8 col-lg-10">
      <div class="card custom-card shadow-sm mb-4">
        <div class="position-relative">
          {% if post.images %}
          <!-- Image Carousel -->
          <div id="carousel{{ post.id }}" class="carousel slide" data-bs-ride="carousel">
            <!-- Garden badge moved to user info section -->
            <div class="carousel-inner">
              {% for image in post.images %}
              <div class="carousel-item {% if loop.first %}active{% endif %}">
                <img src="{{ url_for('static', filename='post_pics/' + image.image_file) }}"
                     class="d-block w-100" alt="{{ image.caption }}"
                     style="border-top-left-radius: 1.5rem; border-top-right-radius: 1.5rem; object-fit: cover; height: 300px;">
              </div>
              {% endfor %}
            </div>
          </div>

          <!-- Custom Carousel Controls -->
          {% if post.images|length > 1 %}
          <div class="d-flex justify-content-between align-items-center px-4 py-2"
               style="background-color: #f8f9fc; border-bottom: 1px solid #e3e6f0;">
            <!-- Left Control -->
            <button type="button"
                    data-bs-target="#carousel{{ post.id }}"
                    data-bs-slide="prev"
                    class="btn btn-sm btn-light rounded-circle"
                    style="width: 32px; height: 32px; padding: 0; border: 1px solid #e3e6f0;">
              <i class="fas fa-chevron-left text-success"></i>
            </button>

            <!-- Indicators -->
            <div class="carousel-indicators position-relative bottom-0 m-0" style="z-index: 0;">
              {% for image in post.images %}
              <button type="button"
                      data-bs-target="#carousel{{ post.id }}"
                      data-bs-slide-to="{{ loop.index0 }}"
                      class="{% if loop.first %}active{% endif %}"
                      style="width: 8px; height: 8px; border-radius: 50%; background-color: #1cc88a;"
                      {% if loop.first %}aria-current="true"{% endif %}
                      aria-label="Slide {{ loop.index }}"></button>
              {% endfor %}
            </div>

            <!-- Right Control -->
            <button type="button"
                    data-bs-target="#carousel{{ post.id }}"
                    data-bs-slide="next"
                    class="btn btn-sm btn-light rounded-circle"
                    style="width: 32px; height: 32px; padding: 0; border: 1px solid #e3e6f0;">
              <i class="fas fa-chevron-right text-success"></i>
            </button>
          </div>
          {% endif %}
          {% endif %}
        </div>

        <div class=" p-4 position-relative">
          {% if post.author == current_user %}
          <div class="position-absolute top-0 end-0 {% if post.garden and not post.images %}mt-5 pt-2 me-3{% else %}m-3{% endif %}" style="z-index: 10;">
            <a class="btn btn-sm"
               href="{{ url_for('posts.updatePost', postId=post.id) }}"
               style="background-color: rgba(28, 200, 138, 0.1);
                      color: #1cc88a;
                      border-radius: 0.5rem;
                      border: 1px solid rgba(28, 200, 138, 0.2);">
              <i class="fas fa-edit me-1"></i> Edit
            </a>
          </div>
          {% endif %}

          <!-- Garden badge moved to user info section -->

          <div class="d-flex align-items-center mb-2">
            <img src="{{ url_for('static', filename='profilePics/' + post.author.image_file) }}"
                class="rounded-circle me-3" alt="{{ post.author.firstName }} {{ post.author.lastName }}"
                style="width: 50px; height: 50px; object-fit: cover; border: 2px solid rgba(28, 200, 138, 0.2);">
            <div>
              <h6 class="mb-0 fw-bold">
                <a href="{{ url_for('users.page_user', username=post.author.username) }}"
                   class="text-decoration-none text-dark hover-success">
                  {{ post.author.firstName }} {{ post.author.lastName }}
                </a>
              </h6>
              <small class="text-muted">{{ post.date_posted.strftime('%B %d, %Y') }} · {{ post.read_time }} min read</small>
              {% if post.garden %}
              <div class="mt-1">
                <a href="#" class="text-decoration-none">
                  <span class="badge bg-success px-2 py-1"
                        style="border-radius: 0.5rem;
                               box-shadow: 0 2px 4px rgba(28, 200, 138, 0.2);">
                    <i class="fas fa-leaf me-1"></i> {{ post.garden.name }}
                  </span>
                </a>
              </div>
              {% endif %}
            </div>
          </div>

          <h4 class="font-weight-bold mb-2">
            <span class="text-success">{{ post.title }}</span>
          </h4>

          <!-- Post Tags moved to footer -->

          <!-- Garden Details Tags -->
          {% if post.garden_id or post.garden_type or post.garden_size or post.plant_count or post.start_date_formatted %}
          <div class="d-flex flex-wrap gap-2 mb-3">
            {% if post.garden_type %}
            <span class="badge bg-light text-success p-2" style="border-radius: 0.35rem !important;">
              <i class="fas fa-map-marker-alt"></i> {{ post.garden_type }}
            </span>
            {% endif %}
            {% if post.garden_size %}
            <span class="badge bg-light text-success p-2" style="border-radius: 0.35rem !important;">
              <i class="fas fa-ruler-combined"></i> {{ post.garden_size }} sq ft
            </span>
            {% endif %}
            {% if post.plant_count %}
            <span class="badge bg-light text-success p-2" style="border-radius: 0.35rem !important;">
              <i class="fas fa-seedling"></i> {{ post.plant_count }} Plants
            </span>
            {% endif %}
            {% if post.start_date_formatted %}
            <span class="badge bg-light text-success p-2" style="border-radius: 0.35rem !important;">
              <i class="fas fa-clock"></i> Started {{ post.start_date_formatted }}
            </span>
            {% endif %}
          </div>
          {% endif %}

          <p class="mb-3">{{ post.content }}</p>

          <!-- Post Tags and Engagement -->
          <div class="border-top mt-3 pt-3">
            <div class="d-flex justify-content-between align-items-center">
              <div class="d-flex flex-wrap gap-1">
                {% if post.tags %}
                  {% for tag in post.tags %}
                  <span class="badge me-1 mb-1"
                        style="background-color: transparent;
                               color: #6c757d;
                               border-radius: 0.25rem;
                               padding: 0.3rem 0.5rem;
                               border: 1px dashed rgba(108, 117, 125, 0.5);
                               font-weight: 400;
                               font-size: 0.75rem;
                               letter-spacing: 0.02em;">
                    <i class="fas fa-hashtag me-1 opacity-75" style="font-size: 0.7rem;"></i>{{ tag.name }}
                  </span>
                  {% endfor %}
                {% endif %}
              </div>
              <div class="d-flex gap-3">
                <button class="btn btn-sm like-btn"
                        data-post-id="{{ post.id }}"
                        onclick="toggleLike(this)"
                        style="background-color: rgba(28, 200, 138, 0.1);
                               color: #1cc88a;
                               border-radius: 0.5rem;
                               border: 1px solid rgba(28, 200, 138, 0.2);">
                  <i class="far fa-heart me-1"></i> <span class="like-text">Like</span> <span class="like-count"></span>
                </button>
                <button class="btn btn-sm" id="comment-button" onclick="scrollToCommentForm()"
                        style="background-color: rgba(28, 200, 138, 0.1);
                               color: #1cc88a;
                               border-radius: 0.5rem;
                               border: 1px solid rgba(28, 200, 138, 0.2);">
                  <i class="far fa-comment me-1"></i> Comment
                </button>
                <button class="btn btn-sm share-btn"
                        data-post-url="{{ url_for('posts.post', postId=post.id, _external=True) }}"
                        onclick="sharePost(this)"
                        style="background-color: rgba(28, 200, 138, 0.1);
                               color: #1cc88a;
                               border-radius: 0.5rem;
                               border: 1px solid rgba(28, 200, 138, 0.2);">
                  <i class="far fa-share-square me-1"></i> Share
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Comments Section -->
      <div class="card custom-card shadow-sm mt-4 mb-4">
        <div class=" p-4">
          <h4 class="mb-4">Comments</h4>

          <!-- Comment Form -->
          {% if current_user.is_authenticated %}
          <div class="mb-4" id="comment-form-container">
            <form method="POST" action="{{ url_for('posts.post', postId=post.id) }}" id="comment-form">
              {{ form.hidden_tag() }}
              <div class="d-flex">
                <img src="{{ url_for('static', filename='profilePics/' + current_user.image_file) }}"
                    class="rounded-circle me-3 align-self-start"
                    alt="{{ current_user.firstName }} {{ current_user.lastName }}"
                    style="width: 40px; height: 40px; object-fit: cover;">
                <div class="flex-grow-1">
                  <div class="form-group mb-2">
                    {{ form.content(class="form-control", rows=3, placeholder="Write a comment...") }}
                  </div>
                  {{ form.parent_id }}
                  <div class="text-end">
                    {{ form.submit(class="btn btn-success") }}
                  </div>
                </div>
              </div>
            </form>
          </div>
          {% else %}
          <div class="alert alert-info mb-4">
            <a href="{{ url_for('users.login') }}">Log in</a> to leave a comment.
          </div>
          {% endif %}

          <!-- Comments List -->
          <div id="comments-container">
            {% if comments %}
              {% for comment in comments %}
                <div class="comment mb-4" id="comment-{{ comment.id }}">
                  <div class="d-flex">
                    <img src="{{ url_for('static', filename='profilePics/' + comment.author.image_file) }}"
                        class="rounded-circle me-3 align-self-start"
                        alt="{{ comment.author.firstName }} {{ comment.author.lastName }}"
                        style="width: 40px; height: 40px; object-fit: cover;">
                    <div class="flex-grow-1">
                      <div class="d-flex justify-content-between align-items-center mb-1">
                        <h6 class="mb-0 fw-bold">{{ comment.author.firstName }} {{ comment.author.lastName }}</h6>
                        <small class="text-muted">{{ comment.date_posted.strftime('%B %d, %Y at %I:%M %p') }}</small>
                      </div>
                      <p class="mb-2">{{ comment.content }}</p>
                      <div class="d-flex">
                        <button class="btn btn-sm text-muted reply-btn" data-comment-id="{{ comment.id }}">
                          <i class="fas fa-reply me-1"></i> Reply
                        </button>
                        {% if current_user == comment.author %}
                        <form action="{{ url_for('posts.delete_comment', comment_id=comment.id) }}" method="POST" class="ms-2">
                          <button type="submit" class="btn btn-sm text-danger" onclick="return confirm('Are you sure you want to delete this comment?')">
                            <i class="fas fa-trash-alt me-1"></i> Delete
                          </button>
                        </form>
                        {% endif %}
                      </div>

                      <!-- Reply Form (initially hidden) -->
                      <div class="reply-form mt-3" id="reply-form-{{ comment.id }}" style="display: none;">
                        <form method="POST" action="{{ url_for('posts.reply_comment', comment_id=comment.id) }}">
                          {{ form.hidden_tag() }}
                          <div class="form-group mb-2">
                            {{ form.content(class="form-control", rows=2, placeholder="Write a reply...") }}
                          </div>
                          <div class="text-end">
                            <button type="button" class="btn btn-sm btn-outline-secondary cancel-reply me-2">Cancel</button>
                            {{ form.submit(class="btn btn-sm btn-success") }}
                          </div>
                        </form>
                      </div>

                      <!-- Replies -->
                      {% if comment.replies.count() > 0 %}
                      <div class="replies mt-3 ms-4 ps-2 border-start border-2" style="border-color: rgba(28, 200, 138, 0.3) !important;">
                        {% for reply in comment.replies %}
                        <div class="reply mb-3" id="comment-{{ reply.id }}">
                          <div class="d-flex">
                            <img src="{{ url_for('static', filename='profilePics/' + reply.author.image_file) }}"
                                class="rounded-circle me-2 align-self-start"
                                alt="{{ reply.author.firstName }} {{ reply.author.lastName }}"
                                style="width: 30px; height: 30px; object-fit: cover;">
                            <div class="flex-grow-1">
                              <div class="d-flex justify-content-between align-items-center mb-1">
                                <h6 class="mb-0 fw-bold small">{{ reply.author.firstName }} {{ reply.author.lastName }}</h6>
                                <small class="text-muted">{{ reply.date_posted.strftime('%B %d, %Y at %I:%M %p') }}</small>
                              </div>
                              <p class="mb-1 small">{{ reply.content }}</p>
                              {% if current_user == reply.author %}
                              <div>
                                <form action="{{ url_for('posts.delete_comment', comment_id=reply.id) }}" method="POST" class="d-inline">
                                  <button type="submit" class="btn btn-sm text-danger p-0" onclick="return confirm('Are you sure you want to delete this reply?')">
                                    <small><i class="fas fa-trash-alt me-1"></i> Delete</small>
                                  </button>
                                </form>
                              </div>
                              {% endif %}
                            </div>
                          </div>
                        </div>
                        {% endfor %}
                      </div>
                      {% endif %}
                    </div>
                  </div>
                </div>
              {% endfor %}
            {% else %}
              <div class="text-center text-muted py-4">
                <p>No comments yet. Be the first to comment!</p>
              </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  // Function to scroll to comment form and focus on the textarea
  function scrollToCommentForm() {
    const commentForm = document.getElementById('comment-form-container');
    const textarea = document.querySelector('#comment-form textarea');

    if (commentForm) {
      // Scroll to the form with smooth animation
      commentForm.scrollIntoView({ behavior: 'smooth', block: 'center' });

      // Focus on the textarea after scrolling completes
      setTimeout(() => {
        if (textarea) {
          textarea.focus();

          // Add a subtle highlight effect
          textarea.style.transition = 'box-shadow 0.3s ease';
          textarea.style.boxShadow = '0 0 0 3px rgba(28, 200, 138, 0.3)';

          // Remove highlight after 1 second
          setTimeout(() => {
            textarea.style.boxShadow = '';
          }, 1000);
        }
      }, 500);
    } else {
      // If user is not logged in, scroll to the login message
      const loginMessage = document.querySelector('.alert-info');
      if (loginMessage) {
        loginMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }
  }

  document.addEventListener('DOMContentLoaded', function() {
    // Reply button functionality
    const replyButtons = document.querySelectorAll('.reply-btn');
    const cancelButtons = document.querySelectorAll('.cancel-reply');

    replyButtons.forEach(button => {
      button.addEventListener('click', function() {
        const commentId = this.getAttribute('data-comment-id');
        const replyForm = document.getElementById(`reply-form-${commentId}`);
        replyForm.style.display = 'block';

        // Focus on the textarea
        const textarea = replyForm.querySelector('textarea');
        if (textarea) {
          setTimeout(() => {
            textarea.focus();
          }, 100);
        }
      });
    });

    cancelButtons.forEach(button => {
      button.addEventListener('click', function() {
        const replyForm = this.closest('.reply-form');
        replyForm.style.display = 'none';
      });
    });
  });
</script>

{% endblock content %}
