{% extends "layout.html" %}
{% block content %}

<div class="row justify-content-center">
    <!-- Left Column - Blog Posts -->
    <div class="col-xl-8 col-lg-7 col-md-12">
        <!-- Function Bar -->
        <div class="card custom-card shadow-sm mb-4">
            <div class=" p-3">
                <div class="row align-items-center">
                    <!-- Search Box -->
                    <div class="col-lg-6 mb-2 mb-lg-0">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="Search posts..."
                                style="border-radius: 0.75rem 0 0 0.75rem; border: 1px solid rgba(28, 200, 138, 0.2);">
                            <button class="btn btn-light"
                                style="border-radius: 0 0.75rem 0.75rem 0; border: 1px solid rgba(28, 200, 138, 0.2);">
                                <i class="fas fa-search text-success"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>



        {% for post in posts.items %}
        <div class="card custom-card shadow-sm mb-4">
            <div class="position-relative">

                {% if post.images %}
                <!-- Image Carousel -->
                <div id="carousel{{ post.id }}" class="carousel slide" data-bs-ride="carousel">
                    {% if post.garden %}
                    <!-- Garden badge moved to user info section -->
                    {% endif %}
                    <div class="carousel-inner">
                        {% for image in post.images %}
                        <div class="carousel-item {% if loop.first %}active{% endif %}">
                            <img src="{{ url_for('static', filename='post_pics/' + image.image_file) }}"
                                 class="d-block w-100" alt="{{ image.caption }}"
                                 style="border-top-left-radius: 1.5rem; border-top-right-radius: 1.5rem; object-fit: cover; height: 300px;">
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- Custom Carousel Controls -->
                {% if post.images|length > 1 %}
                <div class="d-flex justify-content-between align-items-center px-4 py-2"
                     style="background-color: #f8f9fc; border-bottom: 1px solid #e3e6f0;">
                    <!-- Left Control -->
                    <button type="button"
                            data-bs-target="#carousel{{ post.id }}"
                            data-bs-slide="prev"
                            class="btn btn-sm btn-light rounded-circle"
                            style="width: 32px; height: 32px; padding: 0; border: 1px solid #e3e6f0;">
                        <i class="fas fa-chevron-left text-success"></i>
                    </button>

                    <!-- Indicators -->
                    <div class="carousel-indicators position-relative bottom-0 m-0" style="z-index: 0;">
                        {% for image in post.images %}
                        <button type="button"
                                data-bs-target="#carousel{{ post.id }}"
                                data-bs-slide-to="{{ loop.index0 }}"
                                class="{% if loop.first %}active{% endif %}"
                                style="width: 8px; height: 8px; border-radius: 50%; background-color: #1cc88a;"
                                {% if loop.first %}aria-current="true"{% endif %}
                                aria-label="Slide {{ loop.index }}"></button>
                        {% endfor %}
                    </div>

                    <!-- Right Control -->
                    <button type="button"
                            data-bs-target="#carousel{{ post.id }}"
                            data-bs-slide="next"
                            class="btn btn-sm btn-light rounded-circle"
                            style="width: 32px; height: 32px; padding: 0; border: 1px solid #e3e6f0;">
                        <i class="fas fa-chevron-right text-success"></i>
                    </button>
                </div>
                {% endif %}
                {% endif %}
            </div>

            <div class=" p-4 position-relative">
                {% if post.garden and not post.images %}
                <!-- Garden badge moved to user info section -->
                {% endif %}
                <div class="d-flex align-items-center mb-2">
                    <img src="{{ url_for('static', filename='profilePics/' + post.author.image_file) }}"
                        class="rounded-circle me-3" alt="{{ post.author.firstName }} {{ post.author.lastName }}"
                        style="width: 70px; height: 70px; object-fit: cover; border: 2px solid rgba(28, 200, 138, 0.2);">
                    <div>
                        <h6 class="mb-0 fw-bold">
                            <a href="{{ url_for('users.page_user', username=post.author.username) }}"
                               class="text-decoration-none text-dark hover-success">
                                {{ post.author.firstName }} {{ post.author.lastName }}
                            </a>
                        </h6>
                        <small class="text-muted">{{ post.formatted_date }} · {{ post.formatted_read }}</small>
                        {% if post.garden %}
                        <div class="mt-1">
                            <a href="#" class="text-decoration-none">
                                <span class="badge bg-success px-2 py-1"
                                      style="border-radius: 0.5rem;
                                             box-shadow: 0 2px 4px rgba(28, 200, 138, 0.2);">
                                    <i class="fas fa-leaf me-1"></i> {{ post.garden.name }}
                                </span>
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
                <h4 class="font-weight-bold mb-2">
                    <a href="{{ url_for('posts.post', postId=post.id) }}" class="text-success text-decoration-none" style="transition: all 0.2s ease; display: inline-block; border-bottom: 2px solid transparent;" onmouseover="this.style.transform='translateY(-2px)';this.style.color='#15a76c';this.style.borderBottomColor='#15a76c';" onmouseout="this.style.transform='translateY(0)';this.style.color='';this.style.borderBottomColor='transparent';" >
                        {{ post.title }}
                    </a>
                </h4>



                <!-- Garden Details Tags -->
                {% if post.garden_id or post.garden_type or post.garden_size or post.plant_count or post.start_date_formatted %}
                <div class="d-flex flex-wrap gap-2 mb-3">
                    {% if post.garden_type %}
                    <span class="badge bg-light text-success p-2" style="border-radius: 0.35rem !important;">
                        <i class="fas fa-map-marker-alt"></i> {{ post.garden_type }}
                    </span>
                    {% endif %}
                    {% if post.garden_size %}
                    <span class="badge bg-light text-success p-2" style="border-radius: 0.35rem !important;">
                        <i class="fas fa-ruler-combined"></i> {{ post.garden_size }} sq ft
                    </span>
                    {% endif %}
                    {% if post.plant_count %}
                    <span class="badge bg-light text-success p-2" style="border-radius: 0.35rem !important;">
                        <i class="fas fa-seedling"></i> {{ post.plant_count }} Plants
                    </span>
                    {% endif %}
                    {% if post.start_date_formatted %}
                    <span class="badge bg-light text-success p-2" style="border-radius: 0.35rem !important;">
                        <i class="fas fa-clock"></i> Started {{ post.start_date_formatted }}
                    </span>
                    {% endif %}
                </div>
                {% endif %}

                <p class="mb-3">{{ post.content }}</p>


                <!-- Post Tags and Engagement -->
                <div class="border-top mt-3 pt-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex flex-wrap gap-1">
                            {% if post.tags %}
                                {% for tag in post.tags %}
                                <span class="badge me-1 mb-1"
                                      style="background-color: transparent;
                                             color: #6c757d;
                                             border-radius: 0.5rem;
                                             padding: 0.3rem 0.5rem;
                                             border: 1px dashed rgba(108, 117, 125, 0.5);
                                             font-weight: 400;
                                             font-size: 0.75rem;
                                             letter-spacing: 0.02em;">
                                    <i class="fas fa-hashtag me-1 opacity-75" style="font-size: 0.7rem;"></i>{{ tag.name }}
                                </span>
                                {% endfor %}
                            {% endif %}
                        </div>
                        <div class="d-flex gap-3">
                            <button class="btn btn-sm like-btn"
                                    data-post-id="{{ post.id }}"
                                    onclick="toggleLike(this)"
                                    style="background-color: rgba(28, 200, 138, 0.1);
                                           color: #1cc88a;
                                           border-radius: 0.75rem;
                                           border: 1px solid rgba(28, 200, 138, 0.2);">
                                <i class="far fa-heart me-1"></i> <span class="like-text">Like</span> <span class="like-count"></span>
                            </button>

                            <button class="btn btn-sm share-btn"
                                    data-post-url="{{ url_for('posts.post', postId=post.id, _external=True) }}"
                                    onclick="sharePost(this)"
                                    style="background-color: rgba(28, 200, 138, 0.1);
                                           color: #1cc88a;
                                           border-radius: 0.75rem;
                                           border: 1px solid rgba(28, 200, 138, 0.2);">
                                <i class="far fa-share-square me-1"></i> Share
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}

        <!-- Pagination -->
        {% if posts.pages > 1 %}
        <div class="d-flex justify-content-center gap-2 mb-4">
            {% if posts.page > 1 %}
            <a class="btn btn-outline-success"
               href="{{ url_for('main.index', page=1) }}"
               style="border-radius: 0.75rem; border: 1px solid rgba(28, 200, 138, 0.2);">
                <i class="fas fa-angle-double-left"></i>
            </a>
            <a class="btn btn-outline-success"
               href="{{ url_for('main.index', page=posts.page - 1) }}"
               style="border-radius: 0.75rem; border: 1px solid rgba(28, 200, 138, 0.2);">
                <i class="fas fa-angle-left"></i>
            </a>
            {% endif %}

            {% for num in range(max(1, posts.page - 2), min(posts.pages + 1, posts.page + 3)) %}
                {% if num == posts.page %}
                <a class="btn btn-success"
                   href="{{ url_for('main.index', page=num) }}"
                   style="border-radius: 0.75rem; min-width: 40px;">
                    {{ num }}
                </a>
                {% else %}
                <a class="btn btn-outline-success"
                   href="{{ url_for('main.index', page=num) }}"
                   style="border-radius: 0.75rem; border: 1px solid rgba(28, 200, 138, 0.2); min-width: 40px;">
                    {{ num }}
                </a>
                {% endif %}
            {% endfor %}

            {% if posts.page < posts.pages %}
            <a class="btn btn-outline-success"
               href="{{ url_for('main.index', page=posts.page + 1) }}"
               style="border-radius: 0.75rem; border: 1px solid rgba(28, 200, 138, 0.2);">
                <i class="fas fa-angle-right"></i>
            </a>
            <a class="btn btn-outline-success"
               href="{{ url_for('main.index', page=posts.pages) }}"
               style="border-radius: 0.75rem; border: 1px solid rgba(28, 200, 138, 0.2);">
                <i class="fas fa-angle-double-right"></i>
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div> <!-- Closing div for left column -->

    <!-- Right Column - Garden Events -->
    <div class="col-xl-4 col-lg-5 col-md-12">
        <!-- Community Card -->
        <div class="card custom-card shadow-sm mb-4">
            <div class="p-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h4 class="text-success mb-0">Be Part of Our Community</h4>
                    <a href="{{ url_for('posts.newPost') }}" class="btn btn-success"
                       style="border-radius: 0.75rem; padding: 0.5rem 1rem; font-weight: 500; white-space: nowrap;">
                        <i class="fas fa-plus me-1"></i> Create a Post
                    </a>
                </div>
                <p class="text-muted mb-3">Share your gardening journey, tips, and experiences with fellow gardeners. Your knowledge could help someone grow their perfect garden!</p>

                <div class="mt-3">
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-leaf text-success me-2"></i>
                        <span>Share gardening tips and tricks</span>
                    </div>
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-camera text-success me-2"></i>
                        <span>Post photos of your garden</span>
                    </div>
                    <div class="d-flex align-items-center">
                        <i class="fas fa-users text-success me-2"></i>
                        <span>Connect with other gardeners</span>
                    </div>
                </div>
            </div>
        </div>


        <!-- Upcoming Events Card -->
        {% if current_user.is_authenticated and upcoming_events %}
        <div id="events-card" class="card custom-card shadow-sm mb-4" style="position: sticky;
                      top: 1rem;
                      max-height: calc(100vh - 2rem);
                      overflow-y: auto;">
            <div class="card-body p-4">
                <div class="mb-3">
                    <h4 class="text-success">Upcoming Events</h4>
                </div>

                <div class="upcoming-events-container">

                    <!-- Display events -->
                    <div id="events-list">
                        {% if upcoming_events %}
                            {% for event in upcoming_events[:5] %}
                                <div class="d-flex align-items-start mb-3 pb-3" {% if not loop.last %}style="border-bottom: 1px solid rgba(28, 200, 138, 0.1);"{% endif %}>
                                    <div class="text-center me-3" style="min-width: 45px;">
                                        <h5 class="text-success mb-0">{{ event.start_datetime.day }}</h5>
                                        <small class="text-muted">{{ event.start_datetime.strftime('%b').upper() }}</small>
                                    </div>
                                    <div>
                                        <h6 class="text-success mb-1">{{ event.title }}</h6>
                                        <p class="text-muted mb-1 small">{{ event.description or 'No description provided' }}</p>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-clock text-success me-2"></i>
                                            <small class="text-muted">
                                                {% if event.all_day %}
                                                    All day
                                                {% else %}
                                                    {{ event.start_datetime.strftime('%I:%M %p') }} -
                                                    {% if event.end_datetime %}
                                                        {{ event.end_datetime.strftime('%I:%M %p') }}
                                                    {% else %}
                                                        {{ (event.start_datetime + timedelta(hours=1)).strftime('%I:%M %p') }}
                                                    {% endif %}
                                                {% endif %}
                                            </small>
                                        </div>
                                        {% if event.location %}
                                        <div class="d-flex align-items-center mt-1">
                                            <i class="fas fa-map-marker-alt text-success me-2"></i>
                                            <small class="text-muted">{{ event.location }}</small>
                                        </div>
                                        {% endif %}


                                    </div>
                                </div>
                            {% endfor %}
                        {% endif %}
                    </div>

                    <!-- Hidden events template for JavaScript to use -->
                    <div id="hidden-events" style="display: none;" data-total-events="{{ upcoming_events|length }}">
                        {% for event in upcoming_events[5:] %}
                            <div class="d-flex align-items-start mb-3 pb-3 event-item" data-event-index="{{ loop.index + 5 }}" data-event-id="{{ event.id }}" {% if not loop.last %}style="border-bottom: 1px solid rgba(28, 200, 138, 0.1);"{% endif %}>
                                <div class="text-center me-3" style="min-width: 45px;">
                                    <h5 class="text-success mb-0">{{ event.start_datetime.day }}</h5>
                                    <small class="text-muted">{{ event.start_datetime.strftime('%b').upper() }}</small>
                                </div>
                                <div>
                                    <h6 class="text-success mb-1">{{ event.title }}</h6>
                                    <p class="text-muted mb-1 small">{{ event.description or 'No description provided' }}</p>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-clock text-success me-2"></i>
                                        <small class="text-muted">
                                            {% if event.all_day %}
                                                All day
                                            {% else %}
                                                {{ event.start_datetime.strftime('%I:%M %p') }} -
                                                {% if event.end_datetime %}
                                                    {{ event.end_datetime.strftime('%I:%M %p') }}
                                                {% else %}
                                                    {{ (event.start_datetime + timedelta(hours=1)).strftime('%I:%M %p') }}
                                                {% endif %}
                                            {% endif %}
                                        </small>
                                    </div>
                                    {% if event.location %}
                                    <div class="d-flex align-items-center mt-1">
                                        <i class="fas fa-map-marker-alt text-success me-2"></i>
                                        <small class="text-muted">{{ event.location }}</small>
                                    </div>
                                    {% endif %}


                                </div>
                            </div>
                        {% endfor %}
                    </div>

                    {% if upcoming_events|length > 5 %}
                    <div class="text-center mt-3">
                        <button id="load-more-events" class="btn btn-outline-success w-100">
                            <i class="fas fa-plus-circle me-1"></i> Load More Events
                        </button>
                    </div>
                    {% endif %}

                    {% if upcoming_events|length == 0 %}
                    <div class="text-center py-4">
                        <i class="far fa-calendar-alt text-success mb-3" style="font-size: 2rem;"></i>
                        <p class="text-muted mb-0">No upcoming events</p>
                        {% if not current_user.is_authenticated %}
                        <p class="small text-muted mt-2">Log in to view your events</p>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div> <!-- Closing div for row -->
{% endblock content%}
