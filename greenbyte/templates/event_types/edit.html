{% extends "layout.html" %}
{% block content %}
<div class="container-fluid">
  <!-- Page Heading -->
  <div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Edit Event Type</h1>
    <a href="{{ url_for('event_types.list_event_types') }}" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
      <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Event Types
    </a>
  </div>

  <div class="row">
    <div class="col-lg-8">
      <div class="card shadow mb-4">
        <div class="card-header py-3">
          <h6 class="m-0 font-weight-bold text-primary">Edit Event Type</h6>
        </div>
        <div class="card-body">
          <form method="POST" action="{{ url_for('event_types.edit_event_type', event_type_id=event_type.id) }}">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            <div class="form-group mb-4">
              <label for="name" class="form-label fw-bold">Name</label>
              <input
                type="text"
                class="form-control"
                id="name"
                name="name"
                placeholder="Enter event type name"
                value="{{ event_type.name }}"
                required
                style="border: 1px solid rgba(28, 200, 138, 0.2);
                       border-radius: 0.75rem;
                       padding: 0.75rem;
                       background: rgba(255, 255, 255, 0.9);"
              >
            </div>

            <div class="form-group mb-4">
              <label for="color" class="form-label fw-bold">Color</label>
              <div class="input-group">
                <input
                  type="color"
                  class="form-control form-control-color"
                  id="color"
                  name="color"
                  value="{{ event_type.color }}"
                  title="Choose your color"
                  style="height: 50px; width: 100px;"
                >
                <input
                  type="text"
                  class="form-control"
                  id="colorHex"
                  placeholder="{{ event_type.color }}"
                  readonly
                  style="border: 1px solid rgba(28, 200, 138, 0.2);
                         border-radius: 0 0.75rem 0.75rem 0;
                         padding: 0.75rem;
                         background: rgba(255, 255, 255, 0.9);"
                >
              </div>
              <div class="mt-3">
                <label class="form-label">Preview:</label>
                <div id="colorPreview" class="p-3 rounded" style="background-color: {{ event_type.color }}15; border-left: 4px solid {{ event_type.color }};">
                  <span class="fw-bold">Sample Event</span>
                </div>
              </div>
            </div>

            <div class="form-group text-center mt-4">
              <button type="submit" class="btn btn-success px-4">
                <i class="fas fa-save me-2"></i> Update Event Type
              </button>
              <a href="{{ url_for('event_types.list_event_types') }}" class="btn btn-secondary px-4 ms-2">Cancel</a>
            </div>
          </form>
        </div>
      </div>
    </div>

    <div class="col-lg-4">
      <div class="card shadow mb-4">
        <div class="card-header py-3">
          <h6 class="m-0 font-weight-bold text-primary">Event Type Tips</h6>
        </div>
        <div class="card-body">
          <div class="mb-3">
            <h6 class="text-dark"><i class="fas fa-palette text-success me-2"></i>Choosing Colors</h6>
            <p class="small text-muted">Select a color that is visually distinct from the default event types to make your custom events stand out in the calendar.</p>
          </div>

          <div class="mb-3">
            <h6 class="text-dark"><i class="fas fa-lightbulb text-success me-2"></i>Naming Tips</h6>
            <p class="small text-muted">Use clear, concise names that describe the purpose of the event type. For example:</p>
            <ul class="small text-muted">
              <li>Family</li>
              <li>Vacation</li>
              <li>Appointment</li>
              <li>Meeting</li>
              <li>Deadline</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const colorInput = document.getElementById('color');
    const colorHex = document.getElementById('colorHex');
    const colorPreview = document.getElementById('colorPreview');

    // Update color hex value and preview when color changes
    colorInput.addEventListener('input', function() {
      const selectedColor = colorInput.value;
      colorHex.value = selectedColor;

      // Update preview
      colorPreview.style.borderLeftColor = selectedColor;
      colorPreview.style.backgroundColor = `${selectedColor}15`; // 15 is hex for 10% opacity
    });

    // Initialize with default value
    colorHex.value = colorInput.value;
  });
</script>
{% endblock content %}
