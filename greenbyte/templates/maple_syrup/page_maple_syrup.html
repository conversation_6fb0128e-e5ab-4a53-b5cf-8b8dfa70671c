{% extends "layout.html" %}

{% block content %}
<div class="container-fluid">
  <!-- Page Heading -->
  <div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">
      <i class="fas fa-tint text-success mr-2"></i>
      Maple Syrup Tracking
    </h1>
    <div>
      <button class="btn btn-success btn-sm mr-2">
        <i class="fas fa-plus fa-sm"></i> Add Sap Collection
      </button>
      <button class="btn btn-outline-success btn-sm">
        <i class="fas fa-fire fa-sm"></i> New Boiling Session
      </button>
    </div>
  </div>

  <!-- Content Row -->
  <div class="row">
    <!-- Left Column -->
    <div class="col-xl-8 col-lg-7">
      
      <!-- Sap Collection Overview Card -->
      {% include 'maple_syrup/components/sap_collection_card.html' %}
      
      <!-- Recent Boiling Sessions Card -->
      {% include 'maple_syrup/components/boiling_sessions_card.html' %}
      
      <!-- Production Summary Card -->
      {% include 'maple_syrup/components/production_summary_card.html' %}
      
    </div>

    <!-- Right Column -->
    <div class="col-xl-4 col-lg-5">
      
      <!-- Season Stats Card -->
      {% include 'maple_syrup/components/season_stats_card.html' %}
      
      <!-- Equipment Status Card -->
      {% include 'maple_syrup/components/equipment_status_card.html' %}
      
      <!-- Weather Conditions Card -->
      {% include 'maple_syrup/components/weather_card.html' %}
      
    </div>
  </div>
</div>
{% endblock content %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/maple_syrup.js') }}"></script>
{% endblock %}
