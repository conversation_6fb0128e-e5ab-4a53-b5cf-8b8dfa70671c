<!-- Production Summary Card -->
<div class="card shadow mb-4 rounded-custom">
  <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
    <h6 class="m-0 font-weight-bold text-success">
      <i class="fas fa-maple-leaf mr-2"></i>
      Production Summary
    </h6>
    <div class="dropdown no-arrow">
      <a class="dropdown-toggle" href="#" role="button" id="productionDropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
        <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
      </a>
      <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="productionDropdown">
        <div class="dropdown-header">View:</div>
        <a class="dropdown-item" href="#">This Week</a>
        <a class="dropdown-item" href="#">This Month</a>
        <a class="dropdown-item active" href="#">This Season</a>
      </div>
    </div>
  </div>
  <div class="card-body">
    <!-- Season Totals -->
    <div class="row text-center mb-4">
      <div class="col-4">
        <div class="border-right">
          <h4 class="text-success mb-1">12.8</h4>
          <p class="text-muted small mb-0">Gallons Syrup</p>
        </div>
      </div>
      <div class="col-4">
        <div class="border-right">
          <h4 class="text-info mb-1">485</h4>
          <p class="text-muted small mb-0">Gallons Sap</p>
        </div>
      </div>
      <div class="col-4">
        <h4 class="text-warning mb-1">38:1</h4>
        <p class="text-muted small mb-0">Sap Ratio</p>
      </div>
    </div>
    
    <!-- Grade Breakdown -->
    <div class="mb-4">
      <h6 class="text-gray-800 mb-3">Syrup Grade Breakdown</h6>
      <div class="grade-item d-flex justify-content-between align-items-center mb-2">
        <div class="d-flex align-items-center">
          <div class="grade-color bg-warning mr-2" style="width: 12px; height: 12px; border-radius: 50%;"></div>
          <span class="text-sm">Grade A Light</span>
        </div>
        <div class="d-flex align-items-center">
          <span class="text-sm text-gray-600 mr-2">4.2 gal</span>
          <div class="progress" style="width: 60px; height: 6px;">
            <div class="progress-bar bg-warning" style="width: 33%"></div>
          </div>
        </div>
      </div>
      <div class="grade-item d-flex justify-content-between align-items-center mb-2">
        <div class="d-flex align-items-center">
          <div class="grade-color bg-info mr-2" style="width: 12px; height: 12px; border-radius: 50%;"></div>
          <span class="text-sm">Grade A Medium</span>
        </div>
        <div class="d-flex align-items-center">
          <span class="text-sm text-gray-600 mr-2">5.8 gal</span>
          <div class="progress" style="width: 60px; height: 6px;">
            <div class="progress-bar bg-info" style="width: 45%"></div>
          </div>
        </div>
      </div>
      <div class="grade-item d-flex justify-content-between align-items-center">
        <div class="d-flex align-items-center">
          <div class="grade-color bg-secondary mr-2" style="width: 12px; height: 12px; border-radius: 50%;"></div>
          <span class="text-sm">Grade A Dark</span>
        </div>
        <div class="d-flex align-items-center">
          <span class="text-sm text-gray-600 mr-2">2.8 gal</span>
          <div class="progress" style="width: 60px; height: 6px;">
            <div class="progress-bar bg-secondary" style="width: 22%"></div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Season Progress -->
    <div class="mt-3">
      <div class="d-flex justify-content-between align-items-center mb-2">
        <span class="text-sm font-weight-bold text-success">Season Goal Progress</span>
        <span class="text-sm text-gray-600">12.8 / 20 gallons</span>
      </div>
      <div class="progress">
        <div class="progress-bar bg-success" role="progressbar" style="width: 64%" aria-valuenow="64" aria-valuemin="0" aria-valuemax="100"></div>
      </div>
      <p class="text-muted small mt-1 mb-0">7.2 gallons remaining to reach season goal</p>
    </div>
  </div>
</div>
