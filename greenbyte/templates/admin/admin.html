{% extends "layout.html" %}
{% block content %}
<div class="container-fluid">
  <!-- Page Heading -->
  <div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Admin Dashboard</h1>
  </div>

  <!-- Content Row - Stats Cards -->
  <div class="row">
    <!-- Users Card -->
    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-success shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Users</div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.users }}</div>
            </div>
            <div class="col-auto">
              <i class="fas fa-users fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Gardens Card -->
    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-primary shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Gardens</div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.gardens }}</div>
            </div>
            <div class="col-auto">
              <i class="fas fa-leaf fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Zones Card -->
    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-info shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Zones</div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.zones }}</div>
            </div>
            <div class="col-auto">
              <i class="fas fa-map-marker-alt fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Plants Card -->
    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-warning shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Plants</div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.plants }}</div>
            </div>
            <div class="col-auto">
              <i class="fas fa-seedling fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <!-- Plant Details Card -->
    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-success shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Plant Details</div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.plant_details }}</div>
            </div>
            <div class="col-auto">
              <i class="fas fa-info-circle fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Plant Varieties Card -->
    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-primary shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Plant Varieties</div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.plant_varieties }}</div>
            </div>
            <div class="col-auto">
              <i class="fas fa-seedling fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Plant Tracking Card -->
    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-info shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Plant Tracking</div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.plant_tracking }}</div>
            </div>
            <div class="col-auto">
              <i class="fas fa-chart-line fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Harvests Card -->
    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-warning shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Harvests</div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.harvests }}</div>
            </div>
            <div class="col-auto">
              <i class="fas fa-carrot fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <!-- Posts Card -->
    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-success shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Posts</div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.posts }}</div>
            </div>
            <div class="col-auto">
              <i class="fas fa-newspaper fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Comments Card -->
    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-primary shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Comments</div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.comments }}</div>
            </div>
            <div class="col-auto">
              <i class="fas fa-comments fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Clients Card -->
    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-info shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Clients</div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.clients }}</div>
            </div>
            <div class="col-auto">
              <i class="fas fa-user-tie fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Orders Card -->
    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-warning shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Orders</div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.orders }}</div>
            </div>
            <div class="col-auto">
              <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <!-- Calendar Events Card -->
    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-success shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Calendar Events</div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.calendar_events }}</div>
            </div>
            <div class="col-auto">
              <i class="fas fa-calendar fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Inventory Items Card -->
    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-primary shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Inventory Items</div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.inventory_items }}</div>
            </div>
            <div class="col-auto">
              <i class="fas fa-boxes fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Sales Card -->
    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-info shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Sales</div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.sales }}</div>
            </div>
            <div class="col-auto">
              <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Purchases Card -->
    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-warning shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Purchases</div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.purchases }}</div>
            </div>
            <div class="col-auto">
              <i class="fas fa-shopping-bag fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Content Row - Database Tables -->
  <div class="row">
    <!-- Users Table -->
    <div class="col-12 mb-4">
      <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
          <h6 class="m-0 font-weight-bold text-success">Users</h6>
          <div>
            <button class="btn btn-sm btn-success" data-toggle="collapse" data-target="#usersTable" aria-expanded="true">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
        </div>
        <div class="collapse show" id="usersTable">
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-bordered dataTable" width="100%" cellspacing="0">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Username</th>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Access Level</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {% for user in users %}
                  <tr>
                    <td>{{ user.id }}</td>
                    <td>{{ user.username }}</td>
                    <td>{{ user.firstName }} {{ user.lastName }}</td>
                    <td>{{ user.email }}</td>
                    <td>
                      <span class="badge {% if user.access_level == 'superadmin' %}bg-danger{% elif user.access_level == 'admin' %}bg-warning{% else %}bg-success{% endif %}">
                        {{ user.access_level }}
                      </span>
                    </td>
                    <td>
                      <a href="{{ url_for('users.page_user', username=user.username) }}" class="btn btn-sm btn-info">
                        <i class="fas fa-eye"></i>
                      </a>
                      <button class="btn btn-sm btn-warning" disabled>
                        <i class="fas fa-edit"></i>
                      </button>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Farms Table -->
    <div class="col-12 mb-4">
      <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
          <h6 class="m-0 font-weight-bold text-success">Farms</h6>
          <div>
            <button class="btn btn-sm btn-success" data-toggle="collapse" data-target="#farmsTable" aria-expanded="true">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
        </div>
        <div class="collapse show" id="farmsTable">
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-bordered dataTable" width="100%" cellspacing="0">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Owner</th>
                    <th>Address</th>
                    <th>Members</th>
                    <th>Gardens</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {% for farm in farms %}
                  <tr>
                    <td>{{ farm.id }}</td>
                    <td>{{ farm.name }}</td>
                    <td>{{ farm.owner.username if farm.owner else 'N/A' }}</td>
                    <td>{{ farm.address or 'N/A' }}</td>
                    <td>{{ farm.members|length }}</td>
                    <td>{{ farm.gardens|length if farm.gardens else 0 }}</td>
                    <td>
                      <button class="btn btn-sm btn-info" disabled>
                        <i class="fas fa-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-warning" disabled>
                        <i class="fas fa-edit"></i>
                      </button>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Gardens Table -->
    <div class="col-12 mb-4">
      <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
          <h6 class="m-0 font-weight-bold text-success">Gardens</h6>
          <div>
            <button class="btn btn-sm btn-success" data-toggle="collapse" data-target="#gardensTable" aria-expanded="true">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
        </div>
        <div class="collapse show" id="gardensTable">
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-bordered dataTable" width="100%" cellspacing="0">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Location</th>
                    <th>Owner</th>
                    <th>Farm</th>
                    <th>Zones</th>
                    <th>Last Updated</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {% for garden in gardens %}
                  <tr>
                    <td>{{ garden.id }}</td>
                    <td>{{ garden.name }}</td>
                    <td>{{ garden.location or 'N/A' }}</td>
                    <td>{{ garden.owner.username }}</td>
                    <td>{{ garden.farm.name if garden.farm else 'N/A' }}</td>
                    <td>{{ garden.zones|length }}</td>
                    <td>{{ garden.last_updated.strftime('%Y-%m-%d %H:%M') if garden.last_updated else 'N/A' }}</td>
                    <td>
                      <a href="{{ url_for('gardens.view_gardens') }}" class="btn btn-sm btn-info">
                        <i class="fas fa-eye"></i>
                      </a>
                      <button class="btn btn-sm btn-warning" disabled>
                        <i class="fas fa-edit"></i>
                      </button>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Zones Table -->
    <div class="col-12 mb-4">
      <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
          <h6 class="m-0 font-weight-bold text-success">Zones</h6>
          <div>
            <button class="btn btn-sm btn-success" data-toggle="collapse" data-target="#zonesTable" aria-expanded="true">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
        </div>
        <div class="collapse show" id="zonesTable">
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-bordered dataTable" width="100%" cellspacing="0">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Garden</th>
                    <th>Plants</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {% for zone in zones %}
                  <tr>
                    <td>{{ zone.id }}</td>
                    <td>{{ zone.name }}</td>
                    <td>{{ zone.garden.name }}</td>
                    <td>{{ zone.plants|length }}</td>
                    <td>
                      <button class="btn btn-sm btn-info" disabled>
                        <i class="fas fa-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-warning" disabled>
                        <i class="fas fa-edit"></i>
                      </button>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Plants Table -->
    <div class="col-12 mb-4">
      <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
          <h6 class="m-0 font-weight-bold text-success">Plants</h6>
          <div>
            <button class="btn btn-sm btn-success" data-toggle="collapse" data-target="#plantsTable" aria-expanded="true">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
        </div>
        <div class="collapse show" id="plantsTable">
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-bordered dataTable" width="100%" cellspacing="0">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Type</th>
                    <th>Variety</th>
                    <th>Zone</th>
                    <th>Garden</th>
                    <th>Status</th>
                    <th>Quantity</th>
                    <th>Planting Date</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {% for plant in plants %}
                  <tr>
                    <td>{{ plant.id }}</td>
                    <td>{{ plant.plant_detail.name }}</td>
                    <td>{{ plant.variety.name if plant.variety else 'N/A' }}</td>
                    <td>{{ plant.zone.name }}</td>
                    <td>{{ plant.zone.garden.name }}</td>
                    <td>{{ plant.status }}</td>
                    <td>{{ plant.quantity }}</td>
                    <td>{{ plant.planting_date.strftime('%Y-%m-%d') if plant.planting_date else 'N/A' }}</td>
                    <td>
                      <button class="btn btn-sm btn-info" disabled>
                        <i class="fas fa-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-warning" disabled>
                        <i class="fas fa-edit"></i>
                      </button>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Plant Details Table -->
    <div class="col-12 mb-4">
      <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
          <h6 class="m-0 font-weight-bold text-success">Plant Details</h6>
          <div>
            <button class="btn btn-sm btn-success" data-toggle="collapse" data-target="#plantDetailsTable" aria-expanded="false">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
        </div>
        <div class="collapse" id="plantDetailsTable">
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-bordered dataTable" width="100%" cellspacing="0">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Scientific Name</th>
                    <th>Category</th>
                    <th>Varieties</th>
                    <th>Plants</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {% for plant_detail in plant_details %}
                  <tr>
                    <td>{{ plant_detail.id }}</td>
                    <td>{{ plant_detail.name }}</td>
                    <td>{{ plant_detail.scientific_name or 'N/A' }}</td>
                    <td>{{ plant_detail.category or 'N/A' }}</td>
                    <td>{{ plant_detail.varieties|length }}</td>
                    <td>{{ plant_detail.plants|length }}</td>
                    <td>
                      <button class="btn btn-sm btn-info" disabled>
                        <i class="fas fa-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-warning" disabled>
                        <i class="fas fa-edit"></i>
                      </button>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Plant Varieties Table -->
    <div class="col-12 mb-4">
      <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
          <h6 class="m-0 font-weight-bold text-success">Plant Varieties</h6>
          <div>
            <button class="btn btn-sm btn-success" data-toggle="collapse" data-target="#plantVarietiesTable" aria-expanded="false">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
        </div>
        <div class="collapse" id="plantVarietiesTable">
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-bordered dataTable" width="100%" cellspacing="0">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Plant Type</th>
                    <th>Plants</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {% for variety in plant_varieties %}
                  <tr>
                    <td>{{ variety.id }}</td>
                    <td>{{ variety.name }}</td>
                    <td>{{ variety.plant_detail.name }}</td>
                    <td>{{ variety.plants|length }}</td>
                    <td>
                      <button class="btn btn-sm btn-info" disabled>
                        <i class="fas fa-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-warning" disabled>
                        <i class="fas fa-edit"></i>
                      </button>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Posts Table -->
    <div class="col-12 mb-4">
      <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
          <h6 class="m-0 font-weight-bold text-success">Posts</h6>
          <div>
            <button class="btn btn-sm btn-success" data-toggle="collapse" data-target="#postsTable" aria-expanded="false">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
        </div>
        <div class="collapse" id="postsTable">
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-bordered dataTable" width="100%" cellspacing="0">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Title</th>
                    <th>Author</th>
                    <th>Category</th>
                    <th>Date Posted</th>
                    <th>Comments</th>
                    <th>Likes</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {% for post in posts %}
                  <tr>
                    <td>{{ post.id }}</td>
                    <td>{{ post.title }}</td>
                    <td>{{ post.author.username }}</td>
                    <td>{{ post.category }}</td>
                    <td>{{ post.date_posted.strftime('%Y-%m-%d %H:%M') }}</td>
                    <td>{{ post.comments|length }}</td>
                    <td>{{ post.like_count }}</td>
                    <td>
                      <button class="btn btn-sm btn-info" disabled>
                        <i class="fas fa-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-warning" disabled>
                        <i class="fas fa-edit"></i>
                      </button>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Comments Table -->
    <div class="col-12 mb-4">
      <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
          <h6 class="m-0 font-weight-bold text-success">Comments</h6>
          <div>
            <button class="btn btn-sm btn-success" data-toggle="collapse" data-target="#commentsTable" aria-expanded="false">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
        </div>
        <div class="collapse" id="commentsTable">
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-bordered dataTable" width="100%" cellspacing="0">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Author</th>
                    <th>Post</th>
                    <th>Content</th>
                    <th>Date Posted</th>
                    <th>Parent</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {% for comment in comments %}
                  <tr>
                    <td>{{ comment.id }}</td>
                    <td>{{ comment.author.username }}</td>
                    <td>{{ comment.post.title }}</td>
                    <td>{{ comment.content[:50] + '...' if comment.content|length > 50 else comment.content }}</td>
                    <td>{{ comment.date_posted.strftime('%Y-%m-%d %H:%M') }}</td>
                    <td>{{ comment.parent.id if comment.parent else 'N/A' }}</td>
                    <td>
                      <button class="btn btn-sm btn-info" disabled>
                        <i class="fas fa-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-warning" disabled>
                        <i class="fas fa-edit"></i>
                      </button>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Calendar Events Table -->
    <div class="col-12 mb-4">
      <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
          <h6 class="m-0 font-weight-bold text-success">Calendar Events</h6>
          <div>
            <button class="btn btn-sm btn-success" data-toggle="collapse" data-target="#calendarEventsTable" aria-expanded="false">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
        </div>
        <div class="collapse" id="calendarEventsTable">
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-bordered dataTable" width="100%" cellspacing="0">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Title</th>
                    <th>User</th>
                    <th>Start Date</th>
                    <th>End Date</th>
                    <th>Type</th>
                    <th>Completed</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {% for event in calendar_events %}
                  <tr>
                    <td>{{ event.id }}</td>
                    <td>{{ event.title }}</td>
                    <td>{{ event.user.username }}</td>
                    <td>{{ event.start_datetime.strftime('%Y-%m-%d %H:%M') }}</td>
                    <td>{{ event.end_datetime.strftime('%Y-%m-%d %H:%M') if event.end_datetime else 'N/A' }}</td>
                    <td>{{ event.calendar_type }}</td>
                    <td>{{ 'Yes' if event.completed else 'No' }}</td>
                    <td>
                      <button class="btn btn-sm btn-info" disabled>
                        <i class="fas fa-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-warning" disabled>
                        <i class="fas fa-edit"></i>
                      </button>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Event Types Table -->
    <div class="col-12 mb-4">
      <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
          <h6 class="m-0 font-weight-bold text-success">Event Types</h6>
          <div>
            <button class="btn btn-sm btn-success" data-toggle="collapse" data-target="#eventTypesTable" aria-expanded="false">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
        </div>
        <div class="collapse" id="eventTypesTable">
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-bordered dataTable" width="100%" cellspacing="0">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Color</th>
                    <th>User</th>
                    <th>Default</th>
                    <th>Events</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {% for event_type in event_types %}
                  <tr>
                    <td>{{ event_type.id }}</td>
                    <td>{{ event_type.name }}</td>
                    <td>
                      <span class="badge" style="background-color: {{ event_type.color }}; color: white;">
                        {{ event_type.color }}
                      </span>
                    </td>
                    <td>{{ event_type.user.username }}</td>
                    <td>{{ 'Yes' if event_type.is_default else 'No' }}</td>
                    <td>{{ event_type.events|length }}</td>
                    <td>
                      <button class="btn btn-sm btn-info" disabled>
                        <i class="fas fa-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-warning" disabled>
                        <i class="fas fa-edit"></i>
                      </button>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  /* Card border styling */
  .border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
  }

  .border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
  }

  .border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
  }

  .border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
  }

  /* Badge styling */
  .badge {
    padding: 0.4em 0.65em;
    font-weight: 500;
    border-radius: 0.25rem;
  }

  .bg-success {
    background-color: #1cc88a !important;
    color: white;
  }

  .bg-warning {
    background-color: #f6c23e !important;
    color: white;
  }

  .bg-danger {
    background-color: #e74a3b !important;
    color: white;
  }

  /* Table styling */
  .dataTable th {
    background-color: rgba(28, 200, 138, 0.05);
  }

  /* Card styling */
  .card {
    border-radius: 0.5rem !important;
    overflow: hidden;
  }

  .card-header {
    background-color: rgba(28, 200, 138, 0.05) !important;
  }
</style>
{% endblock %}

{% block scripts %}
<script src="https://cdn.datatables.net/1.10.25/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.10.25/js/dataTables.bootstrap5.min.js"></script>
<script>
  $(document).ready(function() {
    // Initialize DataTables
    $('.dataTable').DataTable({
      "pageLength": 10,
      "order": [[0, "asc"]],
      "responsive": true,
      "language": {
        "search": "Filter:",
        "lengthMenu": "Show _MENU_ entries",
        "info": "Showing _START_ to _END_ of _TOTAL_ entries"
      }
    });
  });
</script>
{% endblock %}