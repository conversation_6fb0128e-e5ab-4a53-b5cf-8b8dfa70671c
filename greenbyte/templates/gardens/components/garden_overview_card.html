<div class="card garden-overview-card shadow-sm mb-4">
<div class=" p-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h4 class="text-success">Garden Overview</h4>
        <a href="{{ url_for('gardens.add_garden') }}" class="btn garden-btn-rounded">
            <i class="fas fa-plus"></i> New Garden
        </a>
    </div>

    <div class="mb-4">
        <h6 class="text-muted mb-2">Total Gardens</h6>
        <div class="d-flex align-items-center">
            <i class="fas fa-home text-success me-2"></i>
            <span>{{ gardens|length }} Garden{{ 's' if gardens|length != 1 }}</span>
        </div>
    </div>

    <div class="mb-4">
        <h6 class="text-muted mb-2">Active Zones</h6>
        <div class="d-flex align-items-center">
            <i class="fas fa-layer-group text-success me-2"></i>
            {% set total_zones = namespace(count=0) %}
            {% for garden in gardens %}
                {% set total_zones.count = total_zones.count + garden.zones|length %}
            {% endfor %}
            <span>{{ total_zones.count }} Garden Zone{{ 's' if total_zones.count != 1 }}</span>
        </div>
    </div>

    <div class="mb-4">
        <h6 class="text-muted mb-2">Plant Count</h6>
        <div class="d-flex align-items-center">
            <i class="fas fa-leaf text-success me-2"></i>
            {% set total_plants = namespace(count=0) %}
            {% for garden in gardens %}
                {% for zone in garden.zones %}
                    {% for plant in zone.plants %}
                        {% set total_plants.count = total_plants.count + plant.quantity %}
                    {% endfor %}
                {% endfor %}
            {% endfor %}
            <span>{{ total_plants.count }} Active Plant{{ 's' if total_plants.count != 1 }}</span>
        </div>
    </div>

    <div class="mb-4">
        <h6 class="text-muted mb-2">Last Updated</h6>
        <div class="d-flex align-items-center">
            <i class="fas fa-clock text-success me-2"></i>
            <span>
                {% if gardens %}
                    {% set latest_garden = gardens|sort(attribute='last_updated', reverse=true)|first %}
                    {% if latest_garden.last_updated %}
                        {{ latest_garden.last_updated.strftime('%B %d, %Y at %I:%M %p %Z') }}
                    {% else %}
                        No updates yet
                    {% endif %}
                {% else %}
                    No gardens yet
                {% endif %}
            </span>
        </div>
    </div>

    <!-- Plant Status Section -->
    <div class="mb-4">
        <div class="d-flex justify-content-between align-items-center mb-2">
            <h6 class="text-muted mb-0">Plant Status</h6>

        </div>
        <div class="task-list">
            {% set status_counts = namespace(growing=0, mature=0, blooming=0, fruiting=0) %}
            {% for garden in gardens %}
                {% for zone in garden.zones %}
                    {% for plant in zone.plants %}
                        {% if plant.status == 'Growing' %}
                            {% set status_counts.growing = status_counts.growing + 1 %}
                        {% elif plant.status == 'Mature' %}
                            {% set status_counts.mature = status_counts.mature + 1 %}
                        {% elif plant.status == 'Blooming' %}
                            {% set status_counts.blooming = status_counts.blooming + 1 %}
                        {% elif plant.status == 'Fruiting' %}
                            {% set status_counts.fruiting = status_counts.fruiting + 1 %}
                        {% endif %}
                    {% endfor %}
                {% endfor %}
            {% endfor %}

            {% if status_counts.growing > 0 %}
            <div class="bg-light p-2 rounded mb-2 status-item">
                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-success"><i class="fas fa-seedling"></i> Growing Plants</small>
                    <span class="badge bg-warning text-dark">{{ status_counts.growing }}</span>
                </div>
            </div>
            {% endif %}

            {% if status_counts.mature > 0 %}
            <div class="bg-light p-2 rounded mb-2 status-item">
                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-success"><i class="fas fa-leaf"></i> Mature Plants</small>
                    <span class="badge bg-success">{{ status_counts.mature }}</span>
                </div>
            </div>
            {% endif %}

            {% if status_counts.blooming > 0 %}
            <div class="bg-light p-2 rounded mb-2 status-item">
                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-success"><i class="fas fa-seedling"></i> Blooming Plants</small>
                    <span class="badge bg-info">{{ status_counts.blooming }}</span>
                </div>
            </div>
            {% endif %}

            {% if status_counts.fruiting > 0 %}
            <div class="bg-light p-2 rounded mb-2 status-item">
                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-success"><i class="fas fa-apple-alt"></i> Fruiting Plants</small>
                    <span class="badge bg-danger">{{ status_counts.fruiting }}</span>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
</div>
