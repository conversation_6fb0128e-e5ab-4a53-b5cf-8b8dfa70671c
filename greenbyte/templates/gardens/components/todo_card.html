<!-- TODO Tasks Card -->
<div id="todo-card" class="card shadow-sm mb-4" style="
            background: white;
            border: 1px solid rgba(54, 185, 204, 0.3);
            border-radius: 1rem !important;
            border-bottom: 4px solid rgba(54, 185, 204, 0.3);
            ">
    <div class=" p-4">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h4 style="color: #36b9cc;">TODO Tasks</h4>
            <a href="{{ url_for('main.add_calendar_event') }}?calendar_type=todo" class="btn" style="background: rgba(54, 185, 204, 0.1);
                        color: #36b9cc;
                        border: 1px solid rgba(54, 185, 204, 0.2);
                        border-radius: 0.5rem;
                        transition: all 0.2s ease;">
                <i class="fas fa-plus"></i> Add TODO
            </a>
        </div>

        <div class="todo-list-container">
            <!-- Debug info -->
            <div class="debug-info" style="display: none;">
                <h5>Debug Info:</h5>
                <p>Garden Events: {{ garden_events|length if garden_events else 0 }} gardens</p>
                <p>Zone Events: {{ zone_events|length if zone_events else 0 }} zones</p>
                <p>Plant Events: {{ plant_events|length if plant_events else 0 }} plants</p>

                {% if garden_events %}
                    <p>Garden IDs with events: {{ garden_events.keys()|list }}</p>
                    {% for garden_id, events in garden_events.items() %}
                        {% for event in events %}
                            <p>Event ID: {{ event.id }}, Title: {{ event.title }}, Type: {{ event.calendar_type }}</p>
                        {% endfor %}
                    {% endfor %}
                {% endif %}

                {% if zone_events %}
                    <p>Zone IDs with events: {{ zone_events.keys()|list }}</p>
                    {% for zone_id, events in zone_events.items() %}
                        {% for event in events %}
                            <p>Event ID: {{ event.id }}, Title: {{ event.title }}, Type: {{ event.calendar_type }}</p>
                        {% endfor %}
                    {% endfor %}
                {% endif %}

                {% if plant_events %}
                    <p>Plant IDs with events: {{ plant_events.keys()|list }}</p>
                    {% for plant_id, events in plant_events.items() %}
                        {% for event in events %}
                            <p>Event ID: {{ event.id }}, Title: {{ event.title }}, Type: {{ event.calendar_type }}</p>
                        {% endfor %}
                    {% endfor %}
                {% endif %}
            </div>

            {% set todo_events = [] %}
            {% set processed_event_ids = [] %}

            <!-- Add standalone TODO tasks -->
            {% if todo_events_list %}
                {% for event in todo_events_list %}
                    {% if event.id not in processed_event_ids %}
                        {% set todo_data = {
                            'id': event.id,
                            'title': event.title,
                            'start_datetime': event.start_datetime,
                            'type': 'standalone',
                            'garden_name': None,
                            'zone_name': None,
                            'plant_name': None
                        } %}
                        {% set _ = todo_events.append(todo_data) %}
                        {% set _ = processed_event_ids.append(event.id) %}
                    {% endif %}
                {% endfor %}
            {% endif %}

            <!-- Process garden, zone, and plant events in order of specificity -->
            <!-- First, add plant-specific events -->
            {% for garden in gardens %}
                {% for zone in garden.zones %}
                    {% for plant in zone.plants %}
                        {% if plant_events and plant.id in plant_events %}
                            {% for event in plant_events[plant.id] %}
                                {% if event.calendar_type|lower == 'todo' and not event.completed and event.id not in processed_event_ids %}
                                    {% set todo_data = {
                                        'id': event.id,
                                        'title': event.title,
                                        'start_datetime': event.start_datetime,
                                        'type': 'plant',
                                        'garden_name': garden.name,
                                        'zone_name': zone.name,
                                        'plant_name': plant.plant_detail.name
                                    } %}
                                    {% set _ = todo_events.append(todo_data) %}
                                    {% set _ = processed_event_ids.append(event.id) %}
                                {% endif %}
                            {% endfor %}
                        {% endif %}
                    {% endfor %}
                {% endfor %}
            {% endfor %}

            <!-- Then, add zone-specific events -->
            {% for garden in gardens %}
                {% for zone in garden.zones %}
                    {% if zone_events and zone.id in zone_events %}
                        {% for event in zone_events[zone.id] %}
                            {% if event.calendar_type|lower == 'todo' and not event.completed and event.id not in processed_event_ids %}
                                {% set todo_data = {
                                    'id': event.id,
                                    'title': event.title,
                                    'start_datetime': event.start_datetime,
                                    'type': 'zone',
                                    'garden_name': garden.name,
                                    'zone_name': zone.name,
                                    'plant_name': None
                                } %}
                                {% set _ = todo_events.append(todo_data) %}
                                {% set _ = processed_event_ids.append(event.id) %}
                            {% endif %}
                        {% endfor %}
                    {% endif %}
                {% endfor %}
            {% endfor %}

            <!-- Finally, add garden-specific events -->
            {% for garden in gardens %}
                {% if garden_events and garden.id in garden_events %}
                    {% for event in garden_events[garden.id] %}
                        {% if event.calendar_type|lower == 'todo' and not event.completed and event.id not in processed_event_ids %}
                            {% set todo_data = {
                                'id': event.id,
                                'title': event.title,
                                'start_datetime': event.start_datetime,
                                'type': 'garden',
                                'garden_name': garden.name,
                                'zone_name': None,
                                'plant_name': None
                            } %}
                            {% set _ = todo_events.append(todo_data) %}
                            {% set _ = processed_event_ids.append(event.id) %}
                        {% endif %}
                    {% endfor %}
                {% endif %}
            {% endfor %}

            <!-- Sort TODO tasks by date -->
            {% set todo_events = todo_events|sort(attribute='start_datetime') %}

            <!-- Display TODO tasks -->
            <div id="todo-list">
                {% if todo_events|length > 0 %}
                    {% for todo in todo_events[:5] %}
                        <div class="todo-item d-flex align-items-center p-2 mb-2 rounded"
                             data-todo-id="{{ todo.id }}">
                            <div class="me-2" style="min-width: 30px">
                                <div class="form-check">
                                    <input class="form-check-input todo-checkbox" type="checkbox"
                                           data-event-id="{{ todo.id }}"
                                           data-todo-id="{{ todo.id }}"
                                           style="cursor: pointer; width: 20px; height: 20px;">
                                </div>
                            </div>
                            <div class="todo-details ms-2 flex-grow-1">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="todo-title fw-bold">{{ todo.title }}</div>
                                    <small class="text-muted" style="font-size: 0.7rem;">
                                        <i class="far fa-calendar me-1"></i>{{ todo.start_datetime.strftime('%b %d') }}
                                    </small>
                                </div>
                                <div class="d-flex flex-column" style="font-size: 0.7rem;">
                                    {% if todo.garden_name %}
                                        <small class="text-muted mb-1"><i class="fas fa-leaf me-1 text-success"></i>{{ todo.garden_name }}</small>
                                    {% endif %}

                                    {% if todo.zone_name %}
                                        <small class="text-muted mb-1"><i class="fas fa-map-marked-alt me-1 text-success"></i>{{ todo.zone_name }}</small>
                                    {% endif %}

                                    {% if todo.plant_name %}
                                        <small class="text-muted"><i class="fas fa-seedling me-1 text-success"></i>{{ todo.plant_name }}</small>
                                    {% endif %}

                                    {% if not todo.garden_name and not todo.zone_name and not todo.plant_name %}
                                        <small class="text-muted"><i class="fas fa-tasks me-1 text-info"></i>General Task</small>
                                    {% endif %}
                                </div>
                            </div>

                        </div>
                    {% endfor %}

                    <!-- Hidden TODOs for load more functionality -->
                    <div id="hidden-todos" style="display: none;" data-total-todos="{{ todo_events|length }}">
                        {% for todo in todo_events[5:] %}
                            <div class="todo-item d-flex align-items-center p-2 mb-2 rounded"
                                 data-todo-index="{{ loop.index + 5 }}"
                                 data-todo-id="{{ todo.id }}">
                                <div class="me-2" style="min-width: 30px">
                                    <div class="form-check">
                                        <input class="form-check-input todo-checkbox" type="checkbox"
                                               data-event-id="{{ todo.id }}"
                                               data-todo-id="{{ todo.id }}"
                                               style="cursor: pointer; width: 20px; height: 20px;">
                                    </div>
                                </div>
                                <div class="todo-details ms-2 flex-grow-1">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="todo-title fw-bold">{{ todo.title }}</div>
                                        <small class="text-muted" style="font-size: 0.7rem;">
                                            <i class="far fa-calendar me-1"></i>{{ todo.start_datetime.strftime('%b %d') }}
                                        </small>
                                    </div>
                                    <div class="d-flex flex-column" style="font-size: 0.7rem;">
                                        {% if todo.garden_name %}
                                            <small class="text-muted mb-1"><i class="fas fa-leaf me-1 text-success"></i>{{ todo.garden_name }}</small>
                                        {% endif %}

                                        {% if todo.zone_name %}
                                            <small class="text-muted mb-1"><i class="fas fa-map-marked-alt me-1 text-success"></i>{{ todo.zone_name }}</small>
                                        {% endif %}

                                        {% if todo.plant_name %}
                                            <small class="text-muted"><i class="fas fa-seedling me-1 text-success"></i>{{ todo.plant_name }}</small>
                                        {% endif %}

                                        {% if not todo.garden_name and not todo.zone_name and not todo.plant_name %}
                                            <small class="text-muted"><i class="fas fa-tasks me-1 text-info"></i>General Task</small>
                                        {% endif %}
                                    </div>
                                </div>

                            </div>
                        {% endfor %}
                    </div>

                    {% if todo_events|length > 5 %}
                    <div class="text-center mt-3">
                        <button id="load-more-todos" class="btn" style="background: rgba(54, 185, 204, 0.1);
                                    color: #36b9cc;
                                    border: 1px solid rgba(54, 185, 204, 0.2);
                                    border-radius: 0.5rem;
                                    transition: all 0.2s ease;">
                            <i class="fas fa-plus-circle me-1"></i> Load More Tasks
                        </button>
                    </div>
                    {% endif %}
                {% else %}
                    <div class="text-center py-3">
                        <p class="text-muted">No TODO tasks</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>