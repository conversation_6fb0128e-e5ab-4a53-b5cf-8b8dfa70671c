<div id="zone-{{ zone.id }}" class="card shadow mb-4" data-statuses='{{ zone.get_plant_statuses()|tojson }}' data-zone-id="{{ zone.id }}" id="zone-card">
    <div class="card-header py-3 d-flex justify-content-between align-items-center zone-card-header">
        <div class="d-flex align-items-center">
            <h6 class="m-0 d-flex align-items-center gap-2 zone-header" data-zone-id="{{ zone.id }}">
                <i class="fas fa-map-marked-alt zone-icon"></i>
                <span class="zone-name">{{ zone.name }}</span>
                {% if zone_events and zone.id in zone_events and zone_events[zone.id]|length > 0 %}
                <span class="badge rounded-pill bg-warning text-dark ms-2 zone-badge">
                    <i class="fas fa-calendar-alt me-1"></i>{{ zone_events[zone.id]|length }} upcoming
                </span>
                {% endif %}
            </h6>
        </div>
        <div class="d-flex gap-2">
            {% if garden.owner_id == current_user.id %}

            <a href="{{ url_for('gardens.edit_zone', zone_id=zone.id) }}"
               class="btn btn-sm zone-btn"
               onclick="event.stopPropagation();">
                <i class="fas fa-edit"></i>
            </a>
            <a href="{{ url_for('gardens.add_plant', garden_id=garden.id, zone_id=zone.id) }}"
               class="btn btn-sm zone-btn zone-btn-text"
               onclick="event.stopPropagation();">
                <i class="fas fa-seedling me-1"></i>Add Plant
            </a>


            {% endif %}
            <a href="{{ url_for('main.add_calendar_event') }}?zone_id={{ zone.id }}&garden_id={{ garden.id }}&calendar_type=todo"
               class="btn btn-sm zone-todo-btn"
               onclick="event.stopPropagation();">
                <i class="fas fa-tasks me-1"></i>Add TODO
            </a>
        </div>
    </div>

    <!-- Zone Content -->
    <div id="zone-content-{{ zone.id }}">


        <!-- Status Flow Visualization -->
        <div class="border-bottom status-flow">
        <div class="d-flex align-items-center gap-2 overflow-auto py-2 px-4 status-flow-container">
            {% for status in zone.get_plant_statuses() %}
                {% set color_sequence = [
                    {'bg': 'bg-success text-white', 'color': '#28a745'},
                    {'bg': 'bg-info text-white', 'color': '#17a2b8'},
                    {'bg': 'bg-primary text-white', 'color': '#4e73df'},
                    {'bg': 'bg-warning text-white', 'color': '#ffc107'},
                    {'bg': 'bg-pink text-white', 'color': '#e83e8c'},
                    {'bg': 'bg-orange text-white', 'color': '#fd7e14'},
                    {'bg': 'bg-teal text-white', 'color': '#20c997'},
                    {'bg': 'bg-purple text-white', 'color': '#6f42c1'},
                    {'bg': 'bg-cyan text-white', 'color': '#17a2b8'},
                    {'bg': 'bg-indigo text-white', 'color': '#6610f2'}
                ] %}
                {% set status_icons = {
                    'Seedling': 'seedling',
                    'Growing': 'leaf',
                    'Mature': 'tree',
                    'Harvesting': 'cut',
                    'Dormant': 'moon',
                    'Flowering': 'flower',
                    'Fruiting': 'apple-alt',
                    'Transplanted': 'exchange-alt',
                    'Diseased': 'biohazard',
                    'Completed': 'check-circle'
                } %}
                {% set color_index = loop.index0 % color_sequence|length %}
                {% set style = {
                    'bg': color_sequence[color_index].bg,
                    'icon': status_icons[status] if status in status_icons else 'circle',
                    'extra_style': 'background-color: ' ~ color_sequence[color_index].color ~ ' !important;'
                } %}
                <div class="d-flex align-items-center">
                    {% if not loop.first %}
                        <i class="fas fa-chevron-right text-muted mx-2"></i>
                    {% endif %}
                    <span class="badge {{ style.bg }} d-flex align-items-center gap-1"
                          style="font-size: 0.8rem; padding: 0.5rem 1rem; {{ style.get('extra_style', '') }}">
                        <i class="fas fa-{{ style.icon }}"></i>
                        {{ status }}
                    </span>
                </div>
            {% endfor %}
        </div>
    </div>
    <!-- Plants List -->
    <div class="plants-container">
        {% if zone.plants %}
            <!-- Column Headers -->
            <div class="d-flex align-items-center py-2 px-4 border-bottom plant-table-header">
                <div class="col-3">Plant</div>
                <div class="col-2">Quantity</div>
                <div class="col-3">Status</div>
                <div class="col-2">Last Updated</div>
                <div class="col-2">Actions</div>
            </div>

            {% for plant in zone.plants %}
            {% set status_index = zone.get_plant_statuses().index(plant.status) if plant.status in zone.get_plant_statuses() else 0 %}
            {% set color_sequence = [
                {'bg': 'bg-success text-white', 'color': '#28a745'},
                {'bg': 'bg-info text-white', 'color': '#17a2b8'},
                {'bg': 'bg-primary text-white', 'color': '#4e73df'},
                {'bg': 'bg-warning text-white', 'color': '#ffc107'},
                {'bg': 'bg-pink text-white', 'color': '#e83e8c'},
                {'bg': 'bg-orange text-white', 'color': '#fd7e14'},
                {'bg': 'bg-teal text-white', 'color': '#20c997'},
                {'bg': 'bg-purple text-white', 'color': '#6f42c1'},
                {'bg': 'bg-cyan text-white', 'color': '#17a2b8'},
                {'bg': 'bg-indigo text-white', 'color': '#6610f2'}
            ] %}
            {% set status_icons = {
                'Seedling': 'seedling',
                'Growing': 'leaf',
                'Mature': 'tree',
                'Harvesting': 'cut',
                'Dormant': 'moon',
                'Flowering': 'flower',
                'Fruiting': 'apple-alt',
                'Transplanted': 'exchange-alt',
                'Diseased': 'biohazard',
                'Completed': 'check-circle'
            } %}
            {% set color_index = status_index % color_sequence|length %}
            {% set style = {
                'bg': color_sequence[color_index].bg,
                'icon': status_icons[plant.status] if plant.status in status_icons else 'circle',
                'extra_style': 'background-color: ' ~ color_sequence[color_index].color ~ ' !important;'
            } %}
            <div class="plant-row d-flex align-items-center py-2 px-4 border-bottom" data-plant-id="{{ plant.id }}">
                <!-- Plant Name & Variety -->
                <div class="col-3 d-flex align-items-center gap-2">
                    <span class="plant-name">
                        {{ plant.plant_detail.name }}
                    </span>
                    {% if plant.variety %}
                    <small class="text-muted">{{ plant.variety.name }}</small>
                    {% endif %}
                </div>

                <!-- Quantity -->
                <div class="col-2">
                    <span class="badge bg-light text-success">
                        <i class="fas fa-seedling"></i> {{ plant.quantity }}
                    </span>
                </div>

                <!-- Status -->
                <div class="col-3 d-flex">
                    <span class="badge {{ style.bg }} d-inline-flex align-items-center gap-1 plant-status-badge"
                          style="{{ style.extra_style }}">
                        <i class="fas fa-{{ style.icon }}"></i>
                        {{ plant.status }}
                    </span>
                </div>

                <!-- Last Updated -->
                {% set latest_tracking = plant.growth_stages|first %}
                <div class="col-2 text-muted">
                    <small>
                        <i class="far fa-clock me-1"></i>
                        {% if latest_tracking %}
                            {{ latest_tracking.date_logged|timeago }}
                        {% else %}
                            {{ plant.planting_date|timeago }}
                        {% endif %}
                    </small>
                </div>

                <!-- Actions -->
                <div class="col-2 d-flex gap-1">
                    <div class="dropdown">
                        <button class="btn btn-sm btn-light px-2 py-1" onclick="toggleStatusOptions('status-dropdown-{{ plant.id }}')">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <ul id="status-dropdown-{{ plant.id }}" class="dropdown-menu dropdown-menu-end plant-dropdown-menu">
                            {% for status in zone.get_plant_statuses() %}
                            <li>
                                {% if status == plant.status %}
                                <!-- Current status - not clickable -->
                                <span class="dropdown-item py-1 text-muted d-flex align-items-center">
                                    <i class="fas fa-check me-2 text-success"></i>
                                    {{ status }}
                                </span>
                                {% else %}
                                <!-- Other statuses - clickable -->
                                <a class="dropdown-item py-1 status-update-link"
                                   href="/api/plant/{{ plant.id }}/status/{{ status }}"
                                   onclick="return simpleUpdateStatus(event, {{ plant.id }}, '{{ status }}');"
                                   data-plant-id="{{ plant.id }}"
                                   data-status="{{ status }}"
                                   data-csrf-token="{{ csrf_token() }}">
                                    {{ status }}
                                </a>
                                {% endif %}
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                    {% if garden.zones|length > 1 %}
                    <div class="dropdown">
                        <button class="btn btn-sm btn-light px-2 py-1" onclick="toggleMoveOptions('move-dropdown-{{ plant.id }}')">
                            <i class="fas fa-exchange-alt"></i>
                        </button>
                        <ul id="move-dropdown-{{ plant.id }}" class="dropdown-menu dropdown-menu-end plant-dropdown-menu">
                            {% for available_zone in garden.zones %}
                                {% if available_zone.id != plant.zone_id %}
                                <li>
                                    <a class="dropdown-item py-1 move-plant-link"
                                       href="#"
                                       onclick="event.preventDefault(); movePlant('{{ plant.id }}', '{{ available_zone.id }}', '{{ available_zone.name }}', '{{ csrf_token() }}');"
                                       data-plant-id="{{ plant.id }}"
                                       data-zone-id="{{ available_zone.id }}"
                                       data-zone-name="{{ available_zone.name }}"
                                       data-csrf-token="{{ csrf_token() }}">
                                        {{ available_zone.name }}
                                    </a>
                                </li>
                                {% endif %}
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        {% else %}
        <div class="text-center py-2">
            <small class="text-muted">No plants in this zone yet</small>
        </div>
        {% endif %}
    </div>
    </div> <!-- End of zone-content -->
</div>