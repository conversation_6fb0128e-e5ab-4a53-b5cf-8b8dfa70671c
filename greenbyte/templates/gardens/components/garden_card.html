<div class="card garden-card mb-4">
    <div class="card-header garden-card-header bg-white d-flex justify-content-between align-items-center p-4">
        <div class="d-flex align-items-center">
            <div>
                <h4 class="text-success m-0">{{ garden.name }}</h4>
                <div class="text-muted small">
                    <i class="fas fa-user"></i>
                    <a href="{{ url_for('users.page_user', username=garden.owner.username) }}"
                       class="text-success text-decoration-none"
                       onclick="event.stopPropagation();">
                        {{ garden.owner.firstName }} {{ garden.owner.lastName }}
                    </a>
                    <span class="ms-3">
                        <i class="fas fa-users"></i> {{ garden.members|length }} Members
                    </span>
                    {% if garden.location %}
                        <span class="ms-3">
                            <i class="fas fa-map-marker-alt"></i> {{ garden.location }}
                        </span>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="d-flex gap-2">
            {% if garden.owner_id == current_user.id %}
            <a href="{{ url_for('gardens.edit_garden', garden_id=garden.id) }}"
               class="btn btn-sm garden-btn"
               onclick="event.stopPropagation();">
                <i class="fas fa-edit"></i>
            </a>
            <a href="{{ url_for('gardens.add_zone', garden_id=garden.id) }}"
               class="btn btn-sm garden-btn-rounded"
               onclick="event.stopPropagation();">
                <i class="fas fa-plus me-2"></i>Add Zone
            </a>
            {% endif %}
        </div>
    </div>


    <!-- Garden Content -->
    <div id="garden-content-{{ garden.id }}">
        <!-- Garden Zones -->
        <div class="garden-content p-4">
        <div class="row">
            {% for zone in garden.zones %}
            <div class="col-12">
                {% include 'gardens/components/zone_card.html' %}
            </div>
            {% endfor %}

            {% if garden.zones|length == 0 %}
            <div class="col-12">
                <div class="text-center py-4">
                    <p class="text-muted">No zones in this garden yet</p>
                </div>
            </div>
            {% endif %}
        </div>
        </div> <!-- End of  -->
    </div> <!-- End of garden-content -->
</div>
