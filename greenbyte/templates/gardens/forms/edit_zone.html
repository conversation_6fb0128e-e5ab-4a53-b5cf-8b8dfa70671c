{% extends "layout.html" %}
{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Main Edit Form Column -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow-sm border-0" style="border-radius: 1rem; border: 1px solid rgba(28, 200, 138, 0.2) !important;">
                <div class="card-body p-4">
                    <!-- Header -->
                    <div class="d-flex align-items-center justify-content-between mb-4">
                        <div class="d-flex align-items-center">
                            <h2 class="mb-0 text-dark">Edit Zone</h2>
                            <span class="ms-3 badge" style="background: rgba(28, 200, 138, 0.1); color: #1cc88a;">
                                {{ zone.name }}
                            </span>
                        </div>
                        <a href="{{ url_for('main.add_calendar_event', garden_id=zone.garden_id, zone_id=zone.id, calendar_type='todo') }}" class="btn btn-success" style="border-radius: 0.75rem;">
                            <i class="fas fa-tasks me-1"></i> Add TODO
                        </a>
                    </div>

                    <form method="POST">
                        {{ form.hidden_tag() }}

                        <!-- Zone Name Field -->
                        <div class="mb-4">
                            {{ form.name.label(class="form-label fw-bold") }}
                            {{ form.name(class="form-control",
                                        style="border: 1px solid rgba(28, 200, 138, 0.2);
                                               border-radius: 0.75rem;
                                               padding: 0.75rem;
                                               background: rgba(255, 255, 255, 0.9);") }}
                        </div>

                        <!-- Plant Status Flow Section -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">Plant Status Flow</label>
                            <div class="status-list">
                                {% for status_field in form.plant_statuses %}
                                    <div class="input-group mb-2">
                                        {{ status_field(class="form-control",
                                                      style="border: 1px solid rgba(28, 200, 138, 0.2);
                                                             border-radius: 0.75rem;
                                                             padding: 0.75rem;
                                                             background: rgba(255, 255, 255, 0.9);") }}
                                        {% if not loop.first %}
                                            <button type="button" class="btn btn-outline-danger remove-status ms-2"
                                                    style="border-radius: 0.75rem;">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        {% endif %}
                                    </div>
                                {% endfor %}
                            </div>

                            <!-- Add Status Button -->
                            <button type="button" class="btn btn-sm mt-3" id="add-status"
                                    style="background: rgba(28, 200, 138, 0.1);
                                           color: #1cc88a;
                                           border: 1px solid rgba(28, 200, 138, 0.2);
                                           border-radius: 0.75rem;
                                           padding: 0.5rem 1rem;">
                                <i class="fas fa-plus me-2"></i>Add Status
                            </button>
                        </div>

                        <!-- Submit and Delete Buttons -->
                        <div class="mt-4 d-flex justify-content-between align-items-center">
                            <div>
                                <a href="{{ url_for('gardens.view_gardens') }}" class="btn"
                                   style="background: rgba(133, 135, 150, 0.1);
                                          color: #858796;
                                          border: 1px solid rgba(133, 135, 150, 0.2);
                                          border-radius: 0.75rem;
                                          padding: 0.5rem 1.5rem;">
                                    <i class="fas fa-arrow-left me-2"></i>Back
                                </a>
                            </div>
                            <div>
                                {{ form.submit(class="btn",
                                             style="background: rgba(28, 200, 138, 0.1);
                                                    color: #1cc88a;
                                                    border: 1px solid rgba(28, 200, 138, 0.2);
                                                    border-radius: 0.75rem;
                                                    padding: 0.5rem 1.5rem;") }}

                                <!-- Delete Button -->
                                <button type="button"
                                        class="btn ms-2"
                                        data-bs-toggle="modal"
                                        data-bs-target="#deleteConfirmModal"
                                        style="background: rgba(231, 74, 59, 0.1);
                                               color: #e74a3b;
                                               border: 1px solid rgba(231, 74, 59, 0.2);
                                               border-radius: 0.75rem;
                                               padding: 0.5rem 1.5rem;">
                                    <i class="fas fa-trash me-2"></i>Delete
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- New Status Reference Card -->
        <div class="col-xl-4 col-lg-5">
            <div class="card" style="border: 1px solid rgba(28, 200, 138, 0.2);
                        border-radius: 1rem !important;
                        box-shadow: 0 4px 12px rgba(0,0,0,0.08);
                        position: sticky;
                        top: 1rem;">
                <div class="card-header bg-white d-flex justify-content-between align-items-center p-4"
                     style="border-bottom: 1px solid rgba(28, 200, 138, 0.2);
                            border-top-left-radius: 1rem !important;
                            border-top-right-radius: 1rem !important;">
                    <h5 class="text-success m-0">Status Ideas</h5>
                </div>
                <div class="card-body p-4">
                    <p class="text-muted small mb-3">Common plant status examples you can use in your zone:</p>

                    {% set color_sequence = [
                        {'bg': 'bg-success text-white', 'color': '#28a745'},
                        {'bg': 'bg-info text-white', 'color': '#17a2b8'},
                        {'bg': 'bg-primary text-white', 'color': '#4e73df'},
                        {'bg': 'bg-warning text-white', 'color': '#ffc107'},
                        {'bg': 'bg-pink text-white', 'color': '#e83e8c'},
                        {'bg': 'bg-orange text-white', 'color': '#fd7e14'},
                        {'bg': 'bg-teal text-white', 'color': '#20c997'},
                        {'bg': 'bg-purple text-white', 'color': '#6f42c1'},
                        {'bg': 'bg-cyan text-white', 'color': '#17a2b8'},
                        {'bg': 'bg-indigo text-white', 'color': '#6610f2'}
                    ] %}

                    {% set status_icons = {
                        'Seeded': 'seedling',
                        'Germinating': 'leaf',
                        'Growing': 'spa',
                        'Flowering': 'flower',
                        'Fruiting': 'apple-alt',
                        'Ready for Harvest': 'hand-holding-heart',
                        'Harvested': 'check',
                        'Diseased': 'biohazard',
                        'Completed': 'check-circle'
                    } %}

                    <div class="d-flex flex-column gap-2">
                        {% for status, icon in status_icons.items() %}
                            {% set color_index = loop.index0 % color_sequence|length %}
                            {% set style = {
                                'bg': color_sequence[color_index].bg,
                                'color': color_sequence[color_index].color
                            } %}
                            <div class="d-flex align-items-center gap-2">
                                <span class="badge {{ style.bg }} d-inline-flex align-items-center gap-1"
                                      style="font-size: 0.8rem;
                                             padding: 0.35rem 0.75rem;
                                             background-color: {{ style.color }} !important;
                                             white-space: nowrap;">
                                    <i class="fas fa-{{ icon }}"></i>
                                    {{ status }}
                                </span>
                                <button type="button"
                                        class="btn btn-sm btn-light add-status-suggestion"
                                        data-status="{{ status }}"
                                        style="padding: 0.25rem 0.5rem; font-size: 0.75rem;">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content" style="border-radius: 15px;">
            <div class="modal-header border-0">
                <h5 class="modal-title" id="deleteConfirmModalLabel" style="color: #e74a3b;">
                    <i class="fas fa-exclamation-triangle me-2"></i>Confirm Deletion
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this zone? This action cannot be undone and will delete all plants within this zone.
            </div>
            <div class="modal-footer border-0">
                <button type="button" class="btn" data-bs-dismiss="modal"
                        style="background: rgba(133, 135, 150, 0.1);
                               color: #858796;
                               border: 1px solid rgba(133, 135, 150, 0.2);
                               border-radius: 0.75rem;
                               padding: 0.5rem 1.5rem;">
                    Cancel
                </button>
                <form action="{{ url_for('gardens.delete_zone', zone_id=zone.id) }}" method="POST" class="d-inline">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn ms-2"
                            style="background: rgba(231, 74, 59, 0.1);
                                   color: #e74a3b;
                                   border: 1px solid rgba(231, 74, 59, 0.2);
                                   border-radius: 0.75rem;
                                   padding: 0.5rem 1.5rem;">
                        <i class="fas fa-trash me-2"></i>Delete Zone
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const statusList = document.querySelector('.status-list');
    const addStatusBtn = document.getElementById('add-status');
    const form = document.querySelector('form');

    // Add new status field
    addStatusBtn.addEventListener('click', function() {
        const newField = document.createElement('div');
        newField.className = 'input-group mb-2';
        const fieldCount = statusList.children.length;
        newField.innerHTML = `
            <input class="form-control"
                   name="plant_statuses-${fieldCount}"
                   style="border: 1px solid rgba(28, 200, 138, 0.2);
                          border-radius: 0.75rem;
                          padding: 0.75rem;
                          background: rgba(255, 255, 255, 0.9);"
                   type="text">
            <button type="button" class="btn btn-outline-danger remove-status ms-2"
                    style="border-radius: 0.75rem;">
                <i class="fas fa-times"></i>
            </button>
        `;
        statusList.appendChild(newField);
    });

    // Remove status field
    statusList.addEventListener('click', function(e) {
        if (e.target.closest('.remove-status')) {
            const inputGroup = e.target.closest('.input-group');
            if (statusList.children.length > 1) {
                inputGroup.remove();
                Array.from(statusList.children).forEach((group, index) => {
                    const input = group.querySelector('input');
                    if (input) {
                        input.name = `plant_statuses-${index}`;
                    }
                });
            }
        }
    });

    // Form validation
    form.addEventListener('submit', function(e) {
        const statusInputs = statusList.querySelectorAll('input');
        let hasValue = false;
        statusInputs.forEach(input => {
            if (input.value.trim()) {
                hasValue = true;
            }
        });

        if (!hasValue) {
            e.preventDefault();
            alert('At least one plant status is required.');
        }
    });

    // Add click handlers for status suggestions
    document.querySelectorAll('.add-status-suggestion').forEach(button => {
        button.addEventListener('click', function() {
            const status = this.dataset.status;
            const statusList = document.querySelector('.status-list');
            const fieldCount = statusList.children.length;

            const newField = document.createElement('div');
            newField.className = 'input-group mb-2';
            newField.innerHTML = `
                <input class="form-control"
                       name="plant_statuses-${fieldCount}"
                       value="${status}"
                       style="border: 1px solid rgba(28, 200, 138, 0.2);
                              border-radius: 0.75rem;
                              padding: 0.75rem;
                              background: rgba(255, 255, 255, 0.9);"
                       type="text">
                <button type="button" class="btn btn-outline-danger remove-status ms-2"
                        style="border-radius: 0.75rem;">
                    <i class="fas fa-times"></i>
                </button>
            `;
            statusList.appendChild(newField);
        });
    });
});
</script>

<style>
.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.add-status-suggestion:hover {
    background-color: rgba(28, 200, 138, 0.1) !important;
    color: #1cc88a !important;
}
</style>
{% endblock %}
