<!-- filepath: /Users/<USER>/Documents/programing/GreenByte/greenbyte/templates/add_zone.html -->
{% extends "layout.html" %}
{% block content %}
<div class="row">
    <div class="col-xl-8 col-lg-7">
        <div class="card mb-4" style="border: 1px solid rgba(28, 200, 138, 0.2);
                    border-radius: 1rem !important;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.08);">
            <div class="card-header bg-white d-flex justify-content-between align-items-center p-4"
                 style="border-bottom: 1px solid rgba(28, 200, 138, 0.2);
                        border-top-left-radius: 1rem !important;
                        border-top-right-radius: 1rem !important;">
                <h4 class="text-success m-0">Create a New Zone in {{ garden.name }}</h4>
            </div>
            <div class="card-body p-4">
                <form method="POST" action="{{ url_for('gardens.add_zone', garden_id=garden.id) }}">
                    {{ form.hidden_tag() }}
                    
                    <!-- Zone Name Field -->
                    <div class="mb-4">
                        {{ form.name.label(class="form-label fw-bold") }}
                        {% if form.name.errors %}
                            {{ form.name(class="form-control is-invalid", placeholder="Zone Name", 
                                       style="border: 1px solid rgba(28, 200, 138, 0.2); border-radius: 0.5rem;") }}
                            <div class="invalid-feedback">
                                {% for error in form.name.errors %}
                                    <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.name(class="form-control", placeholder="Zone Name", 
                                       style="border: 1px solid rgba(28, 200, 138, 0.2); border-radius: 0.5rem;") }}
                        {% endif %}
                    </div>

                    <!-- Plant Status Flow Section -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">Plant Status Flow</label>
                        <p class="text-muted small mb-3">Define the progression of plant statuses in this zone. Plants will follow this flow from top to bottom.</p>
                        <div class="status-list">
                            {% for status_field in form.plant_statuses %}
                                <div class="input-group mb-2">
                                    {{ status_field(class="form-control", 
                                                  style="border: 1px solid rgba(28, 200, 138, 0.2);
                                                         border-radius: 0.75rem;
                                                         padding: 0.75rem;
                                                         background: rgba(255, 255, 255, 0.9);") }}
                                    {% if not loop.first %}
                                        <button type="button" class="btn btn-outline-danger remove-status ms-2" 
                                                style="border-radius: 0.75rem;">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    {% endif %}
                                </div>
                            {% endfor %}
                        </div>
                        
                        <!-- Add Status Button -->
                        <button type="button" class="btn btn-sm mt-3" id="add-status"
                                style="background: rgba(28, 200, 138, 0.1); 
                                       color: #1cc88a; 
                                       border: 1px solid rgba(28, 200, 138, 0.2);
                                       border-radius: 0.75rem;
                                       padding: 0.5rem 1rem;">
                            <i class="fas fa-plus me-2"></i>Add Status
                        </button>
                    </div>

                    <!-- Form Buttons -->
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <a href="{{ url_for('gardens.view_gardens') }}" class="btn" 
                           style="background: rgba(28, 200, 138, 0.1); 
                                  color: #1cc88a; 
                                  border: 1px solid rgba(28, 200, 138, 0.2);
                                  border-radius: 0.5rem;
                                  transition: all 0.2s ease;">
                            <i class="fas fa-arrow-left me-2"></i>Back to Gardens
                        </a>
                        {{ form.submit(class="btn",
                                     style="background: rgba(28, 200, 138, 0.1); 
                                            color: #1cc88a; 
                                            border: 1px solid rgba(28, 200, 138, 0.2);
                                            border-radius: 0.5rem;
                                            transition: all 0.2s ease;") }}
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- New Status Reference Card -->
    <div class="col-xl-4 col-lg-5">
        <div class="card" style="border: 1px solid rgba(28, 200, 138, 0.2);
                    border-radius: 1rem !important;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
                    position: sticky;
                    top: 1rem;">
            <div class="card-header bg-white d-flex justify-content-between align-items-center p-4"
                 style="border-bottom: 1px solid rgba(28, 200, 138, 0.2);
                        border-top-left-radius: 1rem !important;
                        border-top-right-radius: 1rem !important;">
                <h5 class="text-success m-0">Status Ideas</h5>
            </div>
            <div class="card-body p-4">
                <p class="text-muted small mb-3">Common plant status examples you can use in your zone:</p>
                
                {% set color_sequence = [
                    {'bg': 'bg-success text-white', 'color': '#28a745'},
                    {'bg': 'bg-info text-white', 'color': '#17a2b8'},
                    {'bg': 'bg-primary text-white', 'color': '#4e73df'},
                    {'bg': 'bg-warning text-white', 'color': '#ffc107'},
                    {'bg': 'bg-pink text-white', 'color': '#e83e8c'},
                    {'bg': 'bg-orange text-white', 'color': '#fd7e14'},
                    {'bg': 'bg-teal text-white', 'color': '#20c997'},
                    {'bg': 'bg-purple text-white', 'color': '#6f42c1'},
                    {'bg': 'bg-cyan text-white', 'color': '#17a2b8'},
                    {'bg': 'bg-indigo text-white', 'color': '#6610f2'}
                ] %}
                {% set status_icons = {
                    'Seedling': 'seedling',
                    'Growing': 'leaf',
                    'Mature': 'tree',
                    'Harvesting': 'cut',
                    'Dormant': 'moon',
                    'Flowering': 'spa',  
                    'Fruiting': 'apple-alt',
                    'Transplanted': 'exchange-alt',
                    'Diseased': 'biohazard',
                    'Completed': 'check-circle'
                } %}
                
                <div class="d-flex flex-column gap-2">
                    {% for status, icon in status_icons.items() %}
                        {% set color_index = loop.index0 % color_sequence|length %}
                        {% set style = {
                            'bg': color_sequence[color_index].bg,
                            'color': color_sequence[color_index].color
                        } %}
                        <div class="d-flex align-items-center gap-2">
                            <span class="badge {{ style.bg }} d-inline-flex align-items-center gap-1" 
                                  style="font-size: 0.8rem; 
                                         padding: 0.35rem 0.75rem;
                                         background-color: {{ style.color }} !important;
                                         white-space: nowrap;">
                                <i class="fas fa-{{ icon }}"></i>
                                {{ status }}
                            </span>
                            <button type="button" 
                                    class="btn btn-sm btn-light add-status-suggestion"
                                    data-status="{{ status }}"
                                    style="padding: 0.25rem 0.5rem; font-size: 0.75rem;">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const statusList = document.querySelector('.status-list');
    const addStatusBtn = document.getElementById('add-status');
    const form = document.querySelector('form');

    // Add new status field
    addStatusBtn.addEventListener('click', function() {
        const newField = document.createElement('div');
        newField.className = 'input-group mb-2';
        const fieldCount = statusList.children.length;
        newField.innerHTML = `
            <input class="form-control" 
                   name="plant_statuses-${fieldCount}" 
                   style="border: 1px solid rgba(28, 200, 138, 0.2);
                          border-radius: 0.75rem;
                          padding: 0.75rem;
                          background: rgba(255, 255, 255, 0.9);"
                   type="text">
            <button type="button" class="btn btn-outline-danger remove-status ms-2"
                    style="border-radius: 0.75rem;">
                <i class="fas fa-times"></i>
            </button>
        `;
        statusList.appendChild(newField);
    });

    // Remove status field
    statusList.addEventListener('click', function(e) {
        if (e.target.closest('.remove-status')) {
            const inputGroup = e.target.closest('.input-group');
            if (statusList.children.length > 1) {
                inputGroup.remove();
                // Update field names
                Array.from(statusList.children).forEach((group, index) => {
                    const input = group.querySelector('input');
                    if (input) {
                        input.name = `plant_statuses-${index}`;
                    }
                });
            }
        }
    });

    // Form validation
    form.addEventListener('submit', function(e) {
        const statusInputs = statusList.querySelectorAll('input');
        let hasValue = false;
        statusInputs.forEach(input => {
            if (input.value.trim()) {
                hasValue = true;
            }
        });
        
        if (!hasValue) {
            e.preventDefault();
            alert('At least one plant status is required.');
        }
    });

    // Add click handlers for status suggestions
    document.querySelectorAll('.add-status-suggestion').forEach(button => {
        button.addEventListener('click', function() {
            const status = this.dataset.status;
            const statusList = document.querySelector('.status-list');
            const fieldCount = statusList.children.length;
            
            const newField = document.createElement('div');
            newField.className = 'input-group mb-2';
            newField.innerHTML = `
                <input class="form-control" 
                       name="plant_statuses-${fieldCount}" 
                       value="${status}"
                       style="border: 1px solid rgba(28, 200, 138, 0.2);
                              border-radius: 0.75rem;
                              padding: 0.75rem;
                              background: rgba(255, 255, 255, 0.9);"
                       type="text">
                <button type="button" class="btn btn-outline-danger remove-status ms-2"
                        style="border-radius: 0.75rem;">
                    <i class="fas fa-times"></i>
                </button>
            `;
            statusList.appendChild(newField);
        });
    });
});
</script>

<style>
.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.status-list .input-group:not(:last-child):after {
    content: '\f078';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    color: #1cc88a;
    position: absolute;
    bottom: -18px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
}

.add-status-suggestion:hover {
    background-color: rgba(28, 200, 138, 0.1) !important;
    color: #1cc88a !important;
}
</style>
{% endblock content %}
