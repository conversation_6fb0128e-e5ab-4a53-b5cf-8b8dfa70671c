{% extends "layout.html" %}

{% block head %}
<link href="{{ url_for('static', filename='css/gardens.css') }}" rel="stylesheet">
<link href="{{ url_for('static', filename='css/zone_card.css') }}" rel="stylesheet">
{% endblock %}

{% block content %}

<div class="row">
    <!-- Left Column - Gardens -->
    <div class="col-xl-8 col-lg-7">
        {% if gardens|length == 0 %}
        <div class="text-center py-5">
            <i class="fas fa-leaf text-success mb-3" style="font-size: 3rem;"></i>
            <h5 class="text-muted">No gardens yet</h5>
            <p class="text-muted">Start by creating your first garden!</p>
            <a href="{{ url_for('gardens.add_garden') }}" class="btn btn-success">
                <i class="fas fa-plus me-2"></i>Create Garden
            </a>
        </div>
        {% endif %}

        <!-- Individual Gardens -->
        {% for garden in gardens %}
        {% include 'gardens/components/garden_card.html' %}
        {% endfor %}
    </div>

    <!-- Right Column - Overall Stats -->
    <div class="col-xl-4 col-lg-5">
        <div class="right-column-container">

            <!-- Garden Overview Card -->
            {% include 'gardens/components/garden_overview_card.html' %}

            <!-- TODO Tasks Card -->
            {% include 'gardens/components/todo_card.html' %}

        </div> <!-- Closing div for right-column-container -->
    </div>
</div> <!-- Closing div for row -->
{% endblock content%}

{% block scripts %}
{{ super() }}
<script src="{{ url_for('static', filename='js/gardens.js') }}"></script>
<script src="{{ url_for('static', filename='js/zone_card.js') }}"></script>
<script src="{{ url_for('static', filename='js/todo_card.js') }}"></script>
<script src="{{ url_for('static', filename='js/garden_collapse.js') }}"></script>
<script src="{{ url_for('static', filename='js/gardens_inline.js') }}"></script>

<script>
// Simple, clean function to update plant status
function simpleUpdateStatus(event, plantId, newStatus) {
    event.preventDefault();

    console.log('Simple update status called:', plantId, newStatus);
    alert('Updating plant ' + plantId + ' to status: ' + newStatus);

    // Get CSRF token from meta tag
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    console.log('CSRF token:', csrfToken);

    // Send POST request
    fetch('/api/plant/' + plantId + '/status/' + newStatus, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        }
    })
    .then(response => {
        console.log('Response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('Response data:', data);
        if (data.success) {
            alert('Status updated successfully!');
            // Reload page to show changes
            window.location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating status: ' + error.message);
    });

    return false;
}

// Simple function to move plant
function simpleMovePlant(event, plantId, zoneId, zoneName) {
    event.preventDefault();

    console.log('Simple move plant called:', plantId, zoneId, zoneName);
    alert('Moving plant ' + plantId + ' to zone: ' + zoneName);

    // Get CSRF token from meta tag
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    console.log('CSRF token:', csrfToken);

    // Send POST request
    fetch('/api/plant/' + plantId + '/move/' + zoneId, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        }
    })
    .then(response => {
        console.log('Response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('Response data:', data);
        if (data.success) {
            alert('Plant moved successfully!');
            // Reload page to show changes
            window.location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error moving plant: ' + error.message);
    });

    return false;
}
</script>
{% endblock %}