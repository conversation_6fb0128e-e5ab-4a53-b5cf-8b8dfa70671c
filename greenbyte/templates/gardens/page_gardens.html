{% extends "layout.html" %}

{% block head %}
<link href="{{ url_for('static', filename='css/gardens.css') }}" rel="stylesheet">
<link href="{{ url_for('static', filename='css/zone_card.css') }}" rel="stylesheet">
{% endblock %}

{% block content %}

<div class="row">
    <!-- Left Column - Gardens -->
    <div class="col-xl-8 col-lg-7">
        {% if gardens|length == 0 %}
        <div class="text-center py-5">
            <i class="fas fa-leaf text-success mb-3" style="font-size: 3rem;"></i>
            <h5 class="text-muted">No gardens yet</h5>
            <p class="text-muted">Start by creating your first garden!</p>
            <a href="{{ url_for('gardens.add_garden') }}" class="btn btn-success">
                <i class="fas fa-plus me-2"></i>Create Garden
            </a>
        </div>
        {% endif %}

        <!-- Individual Gardens -->
        {% for garden in gardens %}
        {% include 'gardens/components/garden_card.html' %}
        {% endfor %}
    </div>

    <!-- Right Column - Overall Stats -->
    <div class="col-xl-4 col-lg-5">
        <div class="right-column-container">

            <!-- Garden Overview Card -->
            {% include 'gardens/components/garden_overview_card.html' %}

            <!-- TODO Tasks Card -->
            {% include 'gardens/components/todo_card.html' %}

        </div> <!-- Closing div for right-column-container -->
    </div>
</div> <!-- Closing div for row -->
{% endblock content%}

{% block scripts %}
{{ super() }}
<script src="{{ url_for('static', filename='js/gardens.js') }}"></script>
<script src="{{ url_for('static', filename='js/zone_card.js') }}"></script>
<script src="{{ url_for('static', filename='js/todo_card.js') }}"></script>
<script src="{{ url_for('static', filename='js/garden_collapse.js') }}"></script>
<script src="{{ url_for('static', filename='js/gardens_inline.js') }}"></script>
{% endblock %}