{% extends "layout.html" %}
{% block content %}
          <!-- Main Content Column -->
          <div class="col-xl-8 col-lg-7">
            <!-- Function Bar -->
            <div class="card shadow-sm mb-4" 
                 style="background: white;
                        border: 1px solid rgba(28, 200, 138, 0.2);
                        border-radius: 1rem !important;
                        box-shadow: 0 8px 24px rgba(0,0,0,0.12) !important;
                        position: sticky;
                        top: 1rem;
                        z-index: 100;">
              <div class="card-body p-3">
                <div class="row align-items-center">
                  <!-- Search Box -->
                  <div class="col-lg-6 mb-2 mb-lg-0">
                    <div class="input-group">
                      <input type="text" 
                             class="form-control" 
                             placeholder="Search posts..." 
                             style="border-radius: 0.5rem 0 0 0.5rem; border: 1px solid rgba(28, 200, 138, 0.2);">
                      <button class="btn btn-light" 
                              style="border-radius: 0 0.5rem 0.5rem 0; border: 1px solid rgba(28, 200, 138, 0.2);">
                        <i class="fas fa-search text-success"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {% for post in posts.items %}
            <!-- Blog Post Card -->
            <div class="card shadow-sm mb-4" style="background: white; 
                        border-radius: 1.5rem !important; 
                        box-shadow: 0 15px 30px rgba(28, 200, 138, 0.15) !important;">
              {% if post.image_file %}
              <div class="position-relative">
                <!-- Carousel -->
                <div id="postCarousel{{ post.id }}" class="carousel slide" data-bs-ride="carousel">
                  <!-- Carousel Items -->
                  <div class="carousel-inner">
                    <div class="carousel-item active">
                      <img src="{{ url_for('static', filename='post_pics/' + post.image_file) }}" 
                           class="d-block w-100"
                           alt="Post Image"
                           style="height: 240px; object-fit: cover; border-top-left-radius: 1.5rem; border-top-right-radius: 1.5rem;">
                    </div>
                  </div>
                </div>
              </div>
              {% endif %}
              
              <div class="card-body p-4">
                <div class="d-flex align-items-center mb-2">
                  <img src="{{ url_for('static', filename='profilePics/' + post.author.image_file) }}"
                    class="rounded-circle me-2" alt="{{ post.author.firstName }} {{ post.author.lastName }}"
                    style="width: 40px; height: 40px;">
                  <div>
                    <h6 class="mb-0 fw-bold">{{ post.author.firstName }} {{ post.author.lastName }}</h6>
                    <small class="text-muted">{{ post.date_posted.strftime('%B %d, %Y') }}</small>
                  </div>
                </div>
                <h4 class="font-weight-bold text-success mb-2">{{ post.title }}</h4>

                <!-- Garden Stats -->
                {% if post.garden %}
                <div class="d-flex flex-wrap gap-2 mb-3">
                  <span class="badge bg-light text-success p-2" style="border-radius: 0.35rem !important;">
                    <i class="fas fa-map-marker-alt"></i> {{ post.garden.name }}
                  </span>
                  <span class="badge bg-light text-success p-2" style="border-radius: 0.35rem !important;">
                    <i class="fas fa-ruler-combined"></i> {{ post.garden.size }} sq ft
                  </span>
                  <span class="badge bg-light text-success p-2" style="border-radius: 0.35rem !important;">
                    <i class="fas fa-seedling"></i> {{ post.garden.plants|length }} Plants
                  </span>
                  <span class="badge bg-light text-success p-2" style="border-radius: 0.35rem !important;">
                    <i class="fas fa-clock"></i> Started {{ post.garden.created_at.strftime('%B %Y') }}
                  </span>
                </div>
                {% endif %}

                <p class="mb-3">{{ post.content }}</p>

                {% if post.garden and post.garden.zones %}
                <!-- Plant/Garden Data Section -->
                <div class="border-top pt-3">
                  <!-- Growing Conditions -->
                  <div class="d-flex gap-2 mb-3">
                    {% for zone in post.garden.zones %}
                    <span class="badge bg-light text-success p-2" style="border-radius: 0.35rem !important;">
                      <i class="fas fa-sun text-warning"></i> {{ zone.sunlight }}
                    </span>
                    <span class="badge bg-light text-success p-2" style="border-radius: 0.35rem !important;">
                      <i class="fas fa-tint text-primary"></i> {{ zone.watering }}
                    </span>
                    <span class="badge bg-light text-success p-2" style="border-radius: 0.35rem !important;">
                      <i class="fas fa-thermometer-half text-danger"></i> Zone {{ zone.temperature }}
                    </span>
                    {% endfor %}
                  </div>

                  <!-- Plants List -->
                  <div class="d-flex flex-wrap gap-2">
                    {% for plant in post.garden.plants %}
                    <div class="bg-light p-2 rounded" style="border: 1px dashed #1cc88a;">
                      <small class="text-success">
                        <i class="fas fa-leaf"></i> {{ plant.name }}
                        <span class="badge bg-success ms-1">{{ plant.status }}</span>
                      </small>
                    </div>
                    {% endfor %}
                  </div>
                </div>
                {% endif %}
              </div>
            </div>
            {% endfor %}

            <!-- Pagination -->
            {% if posts.pages > 1 %}
            <div class="d-flex justify-content-center mt-4">
              {% for page_num in posts.iter_pages(left_edge=1, right_edge=1, left_current=1, right_current=2) %}
                {% if page_num %}
                  {% if posts.page == page_num %}
                    <a class="btn btn-success mx-1" href="{{ url_for('users.page_user', email=user.email, page=page_num) }}">{{ page_num }}</a>
                  {% else %}
                    <a class="btn btn-outline-success mx-1" href="{{ url_for('users.page_user', email=user.email, page=page_num) }}">{{ page_num }}</a>
                  {% endif %}
                {% else %}
                  ...
                {% endif %}
              {% endfor %}
            </div>
            {% endif %}
          </div>

          <!-- Right Column - User Profile and Stats -->
          <div class="col-xl-4 col-lg-5">
            <!-- Profile Card -->
            <div class="card shadow-sm mb-4" style="position: sticky;
                      top: 1rem;
                      background: white;
                      border: 1px solid rgba(28, 200, 138, 0.2);
                      border-radius: 1rem !important;
                      border-bottom: 4px solid rgba(28, 200, 138, 0.2);
                      box-shadow: 0 8px 24px rgba(0,0,0,0.12) !important;">
              <div class="card-body p-4">
                <!-- Profile Header -->
                <div class="d-flex justify-content-between align-items-start mb-4">
                  <div class="d-flex flex-column align-items-center text-center w-100">
                    <img src="{{ url_for('static', filename='profilePics/' + user.image_file) }}"
                      class="rounded-circle mb-3" alt="Profile Picture"
                      style="width: 120px; height: 120px; border: 4px solid #1cc88a;">
                    <h3 class="mb-2">{{ user.firstName }} {{ user.lastName }}</h3>
                    <p class="text-muted mb-3">
                      <i class="fas fa-map-marker-alt me-2"></i>
                      {{ user.location if user.location else "Location not specified" }}
                    </p>
                    {% if current_user.id == user.id %}
                    <a href="{{ url_for('users.account') }}" class="btn btn-outline-success w-100 mb-4" 
                       style="border-radius: 0.75rem; padding: 0.5rem 1.25rem;">
                      <i class="fas fa-edit me-2"></i>Edit Profile
                    </a>
                    {% endif %}
                  </div>
                </div>

                <!-- Bio Section -->
                <div class="mb-4">
                  <h6 class="text-muted mb-2">About Me</h6>
                  <p class="small">{{ user.bio if user.bio else "No bio provided yet." }}</p>
                </div>

                <!-- Garden Statistics -->
                <div class="mb-4">
                  <h6 class="text-muted mb-2">Garden Statistics</h6>
                  <div class="d-flex align-items-center mb-2">
                    <i class="fas fa-leaf text-success me-2"></i>
                    <span>{{ user.gardens|length }} Gardens</span>
                  </div>
                  <div class="d-flex align-items-center">
                    <i class="fas fa-seedling text-success me-2"></i>
                    <span>{{ user.posts|length }} Posts</span>
                  </div>
                </div>

              </div>
            </div>
          </div>
{% endblock content %}
