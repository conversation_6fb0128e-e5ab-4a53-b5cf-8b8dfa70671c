{% extends "layout.html" %}
{% block content %}
<div class="container-fluid">
    <!-- Edit Account Card -->
    <div class="row">
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4" style="border-radius: 15px; border: 1px solid rgba(28, 200, 138, 0.2);">
                <div class="card-header py-3 d-flex align-items-center" style="background-color: white; border-bottom: 1px solid rgba(28, 200, 138, 0.1);">
                    <h6 class="m-0 font-weight-bold" style="color: #1cc88a;">
                        <i class="fas fa-user-edit me-2"></i>Account Settings
                    </h6>
                </div>
                <div class="card-body p-4">
                    <div class="row mb-4">
                        <div class="col-md-4 text-center">
                            <img class="rounded-circle mb-3" src="{{ imageFile }}" alt="Profile Picture" style="width: 150px; height: 150px; border: 4px solid #1cc88a;">
                        </div>
                        <div class="col-md-8">
                            <form method="POST" action="" enctype="multipart/form-data">
                                {{ form.hidden_tag() }}
                                <div class="form-group mb-3">
                                    {{ form.username.label(class="form-label fw-bold") }}
                                    {% if form.username.errors %}
                                        {{ form.username(class="form-control is-invalid", placeholder="Username") }}
                                        <div class="invalid-feedback">
                                            {% for error in form.username.errors %}
                                                <span>{{ error }}</span>
                                            {% endfor %}
                                        </div>
                                    {% else %}
                                        {{ form.username(class="form-control", placeholder="Username",
                                                      style="border-radius: 10px; border-color: rgba(28, 200, 138, 0.2);") }}
                                    {% endif %}
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            {{ form.firstName.label(class="form-label fw-bold") }}
                                            {% if form.firstName.errors %}
                                                {{ form.firstName(class="form-control is-invalid", placeholder="First Name") }}
                                                <div class="invalid-feedback">
                                                    {% for error in form.firstName.errors %}
                                                        <span>{{ error }}</span>
                                                    {% endfor %}
                                                </div>
                                            {% else %}
                                                {{ form.firstName(class="form-control", placeholder="First Name",
                                                              style="border-radius: 10px; border-color: rgba(28, 200, 138, 0.2);") }}
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            {{ form.lastName.label(class="form-label fw-bold") }}
                                            {% if form.lastName.errors %}
                                                {{ form.lastName(class="form-control is-invalid", placeholder="Last Name") }}
                                                <div class="invalid-feedback">
                                                    {% for error in form.lastName.errors %}
                                                        <span>{{ error }}</span>
                                                    {% endfor %}
                                                </div>
                                            {% else %}
                                                {{ form.lastName(class="form-control", placeholder="Last Name",
                                                             style="border-radius: 10px; border-color: rgba(28, 200, 138, 0.2);") }}
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group mb-3">
                                    {{ form.email.label(class="form-label fw-bold") }}
                                    {% if form.email.errors %}
                                        {{ form.email(class="form-control is-invalid", placeholder="Email Address") }}
                                        <div class="invalid-feedback">
                                            {% for error in form.email.errors %}
                                                <span>{{ error }}</span>
                                            {% endfor %}
                                        </div>
                                    {% else %}
                                        {{ form.email(class="form-control", placeholder="Email Address",
                                                  style="border-radius: 10px; border-color: rgba(28, 200, 138, 0.2);") }}
                                    {% endif %}
                                </div>

                                <div class="form-group mb-4">
                                    {{ form.picture.label(class="form-label fw-bold") }}
                                    {{ form.picture(class="form-control", style="border-radius: 10px; border-color: rgba(28, 200, 138, 0.2);") }}
                                    {% if form.picture.errors %}
                                        {% for error in form.picture.errors %}
                                            <span class="text-danger">{{ error }}</span><br>
                                        {% endfor %}
                                    {% endif %}
                                    <small class="text-muted">Upload a new profile picture (optional)</small>
                                </div>

                                <div class="d-flex justify-content-between align-items-center mt-4">
                                    <div>
                                        <a href="{{ url_for('users.page_user', username=current_user.username) }}" class="btn"
                                           style="background: rgba(133, 135, 150, 0.1);
                                                  color: #858796;
                                                  border: 1px solid rgba(133, 135, 150, 0.2);
                                                  border-radius: 0.75rem;
                                                  padding: 0.5rem 1.5rem;">
                                            <i class="fas fa-arrow-left me-2"></i>Back to Profile
                                        </a>
                                    </div>

                                    <div>
                                        {{ form.submit(class="btn",
                                                     style="background: rgba(28, 200, 138, 0.1);
                                                            color: #1cc88a;
                                                            border: 1px solid rgba(28, 200, 138, 0.2);
                                                            border-radius: 0.75rem;
                                                            padding: 0.5rem 1.5rem;") }}
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Password reset button moved to the form buttons section -->
                </div>
            </div>
        </div>

        <!-- Tips Card -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4" style="border-radius: 15px; border: 1px solid rgba(28, 200, 138, 0.2);">
                <div class="card-header py-3" style="background-color: white; border-bottom: 1px solid rgba(28, 200, 138, 0.1);">
                    <h6 class="m-0 font-weight-bold" style="color: #1cc88a;">
                        <i class="fas fa-info-circle me-2"></i>Account Tips
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="font-weight-bold"><i class="fas fa-image text-success me-2"></i>Profile Picture</h6>
                        <p class="small">Upload a square image for best results. Your profile picture will be visible to other garden members.</p>
                    </div>
                    <div class="mb-3">
                        <h6 class="font-weight-bold"><i class="fas fa-envelope text-success me-2"></i>Email Address</h6>
                        <p class="small">Your email is used for account recovery and notifications. Make sure it's up to date.</p>
                    </div>
                    <div class="mb-3">
                        <h6 class="font-weight-bold"><i class="fas fa-lock text-success me-2"></i>Password Security</h6>
                        <p class="small">Use the Reset Password option to change your password regularly for better security.</p>

                        <div class="mt-3">
                            <a href="{{ url_for('users.resetRequest') }}" class="btn w-100"
                               style="background: rgba(78, 115, 223, 0.1);
                                      color: #4e73df;
                                      border: 1px solid rgba(78, 115, 223, 0.2);
                                      border-radius: 0.75rem;
                                      padding: 0.5rem 1rem;">
                                <i class="fas fa-key me-2"></i>Reset Password
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .form-control:focus {
        border-color: rgba(28, 200, 138, 0.5);
        box-shadow: 0 0 0 0.2rem rgba(28, 200, 138, 0.25);
    }

    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: all 0.2s ease;
    }

    .form-label {
        color: #5a5c69;
        margin-bottom: 0.5rem;
    }
</style>
{% endblock content %}
