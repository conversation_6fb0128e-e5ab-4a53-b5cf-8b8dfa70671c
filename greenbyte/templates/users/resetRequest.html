{% extends "layout.html" %}
{% block content %}

    <div class="container">
        <!-- Outer Row -->
        <div class="row justify-content-center">
            <div class="col-xl-10 col-lg-12 col-md-9">
                <div class="card o-hidden border-0 shadow-lg my-5">
                    <div class="card-body p-0">
                        <!-- Nested Row within Card Body -->
                        <div class="row">
                            <div class="col-lg-6 d-none d-lg-block bg-login-image"></div>
                            <div class="col-lg-6">
                                <div class="p-5">
                                    <div class="text-center">
                                        <h1 class="h4 text-gray-900 mb-4">Reset Password<h1>
                                    </div>
                                    <form class="user" method="POST" action>
                                        {{ form.hidden_tag() }}
                                        <div class="form-group">
                                            {% if form.email.errors %}
                                            {{ form.email(class="form-control form-control-user is-invalid", placeholder="Email Address")}}
                                            <div class="invalid-feedback">
                                                {% for error in form.email.errors%}
                                                <span>{{error}}</span>
                                                {% endfor%}
                                            </div>
                                            {% else %}
                                            {{ form.email(class="form-control form-control-user", placeholder="Email Address")}}
                                            {% endif%}
                                        </div>
                                        {{ form.submit(class="btn btn-primary btn-user btn-block")}}
                                    </form>
                                    <hr>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

        </div>

    </div>
    {% endblock content%}