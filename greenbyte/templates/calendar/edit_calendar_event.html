{% extends "layout.html" %}
{% block content %}
<div class="container-fluid">
  <!-- Page Heading -->
  <div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0" style="color: #1cc88a;">Edit Calendar Event</h1>
  </div>

  <div class="row">
    <!-- Main Edit Form Column -->
    <div class="col-xl-8 col-lg-7">
      <div class="card shadow-sm border-0" style="border-radius: 1rem; border: 1px solid rgba(28, 200, 138, 0.2) !important;">
        <div class="card-body p-4">
          <!-- Header -->
          <div class="d-flex align-items-center mb-4">
            <h2 class="mb-0 text-dark">Edit Event</h2>
            <span class="ms-3 badge" style="background: rgba(28, 200, 138, 0.1); color: #1cc88a;">
              {{ event.title }}
            </span>
          </div>
      <form id="editEventForm" action="{{ url_for('main.edit_event', event_id=event.id) }}" method="POST">
        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

        <!-- Title and Location Section -->
        <div class="form-group mb-4">
          <label for="eventTitle" class="form-label fw-bold">Event Title</label>
          <input
            type="text"
            class="form-control form-control-lg"
            id="eventTitle"
            name="eventTitle"
            placeholder="Add title"
            value="{{ event.title }}"
            required
            style="border: 1px solid rgba(28, 200, 138, 0.2);
                   border-radius: 0.75rem;
                   padding: 0.75rem;
                   background: rgba(255, 255, 255, 0.9);"
          />
        </div>
        <div class="form-group mb-4">
          <label for="location" class="form-label fw-bold">Location</label>
          <div class="input-group">
            <span class="input-group-text" style="border: 1px solid rgba(28, 200, 138, 0.2); border-right: none; border-radius: 0.75rem 0 0 0.75rem;"><i class="fas fa-map-marker-alt"></i></span>
            <input
              type="text"
              class="form-control"
              id="location"
              name="location"
              placeholder="Add location or video call"
              value="{{ event.location or '' }}"
              style="border: 1px solid rgba(28, 200, 138, 0.2); border-left: none; border-radius: 0 0.75rem 0.75rem 0; padding: 0.75rem;"
            />
          </div>
        </div>



        <!-- Date and Time Section -->
        <div class="form-group mb-4">
          <label class="form-label fw-bold">Date & Time</label>
          <div class="d-flex align-items-center mb-3">
            <div class="form-check">
              <input
                class="form-check-input"
                type="checkbox"
                id="allDayEvent"
                name="allDayEvent"
                {% if event.all_day %}checked{% endif %}
              />
              <label class="form-check-label" for="allDayEvent">All-day event</label>
            </div>
          </div>

          <div class="row mb-3">
            <label class="col-2 col-form-label">Starts</label>
            <div class="col-5">
              <input
                type="date"
                class="form-control"
                id="startDate"
                name="startDate"
                value="{{ event.start_datetime.strftime('%Y-%m-%d') }}"
                required
                style="border: 1px solid rgba(28, 200, 138, 0.2);
                       border-radius: 0.75rem;
                       padding: 0.75rem;
                       background: rgba(255, 255, 255, 0.9);"
              />
            </div>
            <div class="col-5" id="startTimeContainer" {% if event.all_day %}style="display: none;"{% endif %}>
              <input
                type="time"
                class="form-control"
                id="startTime"
                name="startTime"
                value="{{ event.start_datetime.strftime('%H:%M') if not event.all_day else '00:00' }}"
                required
                style="border: 1px solid rgba(28, 200, 138, 0.2);
                       border-radius: 0.75rem;
                       padding: 0.75rem;
                       background: rgba(255, 255, 255, 0.9);"
              />
            </div>
          </div>

          <div class="row">
            <label class="col-2 col-form-label">Ends</label>
            <div class="col-5">
              <input
                type="date"
                class="form-control"
                id="endDate"
                name="endDate"
                value="{{ event.end_datetime.strftime('%Y-%m-%d') if event.end_datetime else event.start_datetime.strftime('%Y-%m-%d') }}"
                required
                style="border: 1px solid rgba(28, 200, 138, 0.2);
                       border-radius: 0.75rem;
                       padding: 0.75rem;
                       background: rgba(255, 255, 255, 0.9);"
              />
            </div>
            <div class="col-5" id="endTimeContainer" {% if event.all_day %}style="display: none;"{% endif %}>
              <input
                type="time"
                class="form-control"
                id="endTime"
                name="endTime"
                value="{{ event.end_datetime.strftime('%H:%M') if event.end_datetime and not event.all_day else '23:59' }}"
                required
                style="border: 1px solid rgba(28, 200, 138, 0.2);
                       border-radius: 0.75rem;
                       padding: 0.75rem;
                       background: rgba(255, 255, 255, 0.9);"
              />
            </div>
          </div>
        </div>

        <!-- Repeat Section -->
        <div class="form-group mb-4">
          <label class="form-label fw-bold">Repeat Options</label>
          <div class="row mb-2">
            <label class="col-2 col-form-label">Repeat</label>
            <div class="col-10">
              <select class="form-select" id="repeatOption" name="repeatOption" style="border: 1px solid rgba(28, 200, 138, 0.2); border-radius: 0.75rem; padding: 0.75rem; background: rgba(255, 255, 255, 0.9);">
                <option value="none" {% if not event.repeat_type %}selected{% endif %}>Never</option>
                <option value="daily" {% if event.repeat_type == 'daily' %}selected{% endif %}>Every Day</option>
                <option value="weekly" {% if event.repeat_type == 'weekly' %}selected{% endif %}>Every Week</option>
                <option value="biweekly" {% if event.repeat_type == 'biweekly' %}selected{% endif %}>Every 2 Weeks</option>
                <option value="monthly" {% if event.repeat_type == 'monthly' %}selected{% endif %}>Every Month</option>
                <option value="yearly" {% if event.repeat_type == 'yearly' %}selected{% endif %}>Every Year</option>
              </select>
            </div>
          </div>
          <div class="row" id="endRepeatContainer" {% if not event.repeat_type %}style="display: none;"{% endif %}>
            <label class="col-2 col-form-label">End Repeat</label>
            <div class="col-10">
              <select class="form-select mb-2" id="endRepeat" name="endRepeat" style="border: 1px solid rgba(28, 200, 138, 0.2); border-radius: 0.75rem; padding: 0.75rem; background: rgba(255, 255, 255, 0.9);">
                <option value="" {% if not event.repeat_end_date %}selected{% endif %}>Never</option>
                <option value="on" {% if event.repeat_end_date %}selected{% endif %}>On Date</option>
              </select>
              <input
                type="date"
                class="form-control"
                id="endRepeatDate"
                name="endRepeatDate"
                value="{{ event.repeat_end_date.strftime('%Y-%m-%d') if event.repeat_end_date else '' }}"
                {% if not event.repeat_end_date %}style="display: none;"{% endif %}
                style="border: 1px solid rgba(28, 200, 138, 0.2); border-radius: 0.75rem; padding: 0.75rem; background: rgba(255, 255, 255, 0.9);"
              />
            </div>
          </div>
        </div>

        <!-- Calendar Selection -->
        <div class="form-group mb-4">
          <label class="form-label fw-bold">Calendar Type</label>
          <div class="row">
            <label class="col-2 col-form-label">Calendar</label>
            <div class="col-10">
              <select class="form-select" id="calendar" name="calendar" style="border: 1px solid rgba(28, 200, 138, 0.2); border-radius: 0.75rem; padding: 0.75rem; background: rgba(255, 255, 255, 0.9);">
                <option value="work" style="color: #1cc88a" {% if event.calendar_type == 'work' %}selected{% endif %}>Work</option>
                <option value="community" style="color: #4e73df" {% if event.calendar_type == 'community' %}selected{% endif %}>
                  Community
                </option>
                <option value="school" style="color: #f6c23e" {% if event.calendar_type == 'school' %}selected{% endif %}>School</option>
                <option value="personal" style="color: #e74a3b" {% if event.calendar_type == 'personal' %}selected{% endif %}>
                  Personal
                </option>
                <option value="todo" style="color: #36b9cc" {% if event.calendar_type == 'todo' %}selected{% endif %}>
                  TODO Task
                </option>
                <option value="custom" style="color: #858796" {% if event.calendar_type == 'custom' %}selected{% endif %}>
                  Custom Event Type
                </option>
              </select>

              <!-- Custom Event Type Selection (hidden by default) -->
              <div id="customEventTypeContainer" class="mt-3" {% if event.calendar_type != 'custom' %}style="display: none;"{% endif %}>
                <label class="form-label">Select Custom Event Type</label>
                <select class="form-select" id="eventTypeId" name="event_type_id" style="border: 1px solid rgba(28, 200, 138, 0.2); border-radius: 0.75rem; padding: 0.75rem; background: rgba(255, 255, 255, 0.9);">
                  <option value="" selected disabled>Select a custom event type</option>
                  <!-- Custom event types will be loaded via AJAX -->
                </select>
                <div class="d-flex justify-content-end mt-2">
                  <a href="{{ url_for('event_types.new_event_type') }}" target="_blank" class="btn btn-sm btn-outline-success">
                    <i class="fas fa-plus"></i> Create New Type
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Invitees Section -->
        <div class="form-group mb-4">
          <label class="form-label fw-bold">Invitees</label>
          <div class="input-group">
            <span class="input-group-text" style="border: 1px solid rgba(28, 200, 138, 0.2); border-right: none; border-radius: 0.75rem 0 0 0.75rem;"><i class="fas fa-users"></i></span>
            <input
              type="text"
              class="form-control"
              id="invitees"
              name="invitees"
              placeholder="Add invitees (comma separated emails)"
              value="{% for invitee in event.invitees %}{% if invitee.email %}{{ invitee.email }}{% else %}{{ invitee.user.email }}{% endif %}{% if not loop.last %}, {% endif %}{% endfor %}"
              style="border: 1px solid rgba(28, 200, 138, 0.2); border-left: none; border-radius: 0 0.75rem 0.75rem 0; padding: 0.75rem;"
            />
          </div>
        </div>

        <!-- Garden, Zone, and Plant Selection -->
        <div class="form-group mb-4">
          <label class="form-label fw-bold">Garden Details</label>
          <div class="row mb-2">
            <label class="col-2 col-form-label">Garden</label>
            <div class="col-10">
              <select class="form-select" id="garden_id" name="garden_id" onchange="updateZones()" style="border: 1px solid rgba(28, 200, 138, 0.2); border-radius: 0.75rem; padding: 0.75rem; background: rgba(255, 255, 255, 0.9);">
                <option value="">None</option>
                {% for garden in gardens %}
                <option value="{{ garden.id }}" {% if event.garden_id == garden.id %}selected{% endif %}>{{ garden.name }}</option>
                {% endfor %}
              </select>
            </div>
          </div>

          <div class="row mb-2">
            <label class="col-2 col-form-label">Zone</label>
            <div class="col-10">
              <select class="form-select" id="zone_id" name="zone_id" onchange="updatePlants()" style="border: 1px solid rgba(28, 200, 138, 0.2); border-radius: 0.75rem; padding: 0.75rem; background: rgba(255, 255, 255, 0.9);">
                <option value="">None</option>
                <!-- Zones will be populated dynamically based on selected garden -->
                {% if event.zone_id %}
                <option value="{{ event.zone.id }}" selected>{{ event.zone.name }}</option>
                {% endif %}
              </select>
            </div>
          </div>

          <div class="row">
            <label class="col-2 col-form-label">Plant</label>
            <div class="col-10">
              <select class="form-select" id="plant_id" name="plant_id" style="border: 1px solid rgba(28, 200, 138, 0.2); border-radius: 0.75rem; padding: 0.75rem; background: rgba(255, 255, 255, 0.9);">
                <option value="">None</option>
                {% for plant in plants %}
                <option value="{{ plant.id }}" data-zone="{{ plant.zone_id }}" {% if event.plant_id == plant.id %}selected{% endif %}>{{ plant.plant_detail.name }} {% if plant.variety %}({{ plant.variety.name }}){% endif %}</option>
                {% endfor %}
              </select>
            </div>
          </div>
        </div>

        <!-- Alert Section -->
        <div class="form-group mb-4">
          <label class="form-label fw-bold">Notification Settings</label>
          <div class="row mb-3">
            <label class="col-2 col-form-label">Alert</label>
            <div class="col-10">
              <select class="form-select" id="alert" name="alert" style="border: 1px solid rgba(28, 200, 138, 0.2); border-radius: 0.75rem; padding: 0.75rem; background: rgba(255, 255, 255, 0.9);">
                <option value="0" {% if event.alert_before_minutes == 0 %}selected{% endif %}>At time of event</option>
                <option value="5" {% if event.alert_before_minutes == 5 %}selected{% endif %}>5 minutes before</option>
                <option value="10" {% if event.alert_before_minutes == 10 %}selected{% endif %}>10 minutes before</option>
                <option value="15" {% if event.alert_before_minutes == 15 %}selected{% endif %}>15 minutes before</option>
                <option value="30" {% if event.alert_before_minutes == 30 %}selected{% endif %}>30 minutes before</option>
                <option value="60" {% if event.alert_before_minutes == 60 %}selected{% endif %}>1 hour before</option>
                <option value="1440" {% if event.alert_before_minutes == 1440 %}selected{% endif %}>1 day before</option>
              </select>
            </div>
          </div>

          <div class="row">
            <div class="col-10 offset-2">
              <div class="form-check">
                <input
                  class="form-check-input"
                  type="checkbox"
                  id="privateEvent"
                  name="privateEvent"
                  {% if event.is_private %}checked{% endif %}
                />
                <label class="form-check-label" for="privateEvent">Make this event private</label>
              </div>
            </div>
          </div>
        </div>

        <!-- URL -->
        <div class="form-group mb-4">
          <label class="form-label fw-bold">URL</label>
          <div class="input-group">
            <span class="input-group-text" style="border: 1px solid rgba(28, 200, 138, 0.2); border-right: none; border-radius: 0.75rem 0 0 0.75rem;"><i class="fas fa-link"></i></span>
            <input
              type="url"
              class="form-control"
              id="eventUrl"
              name="eventUrl"
              placeholder="Add URL"
              value="{{ event.url or '' }}"
              style="border: 1px solid rgba(28, 200, 138, 0.2); border-left: none; border-radius: 0 0.75rem 0.75rem 0; padding: 0.75rem;"
            />
          </div>
        </div>

        <!-- Notes -->
        <div class="form-group mb-4">
          <label class="form-label fw-bold">Notes</label>
          <textarea
            class="form-control"
            id="notes"
            name="notes"
            rows="3"
            placeholder="Add notes"
            style="border: 1px solid rgba(28, 200, 138, 0.2); border-radius: 0.75rem; padding: 0.75rem; background: rgba(255, 255, 255, 0.9);"
          >{{ event.description or '' }}</textarea>
        </div>

        <!-- Form Buttons -->
        <div class="d-flex justify-content-between align-items-center mt-4">
          <a href="{{ url_for('main.calendar') }}" class="btn"
             style="background: rgba(133, 135, 150, 0.1);
                    color: #858796;
                    border: 1px solid rgba(133, 135, 150, 0.2);
                    border-radius: 0.75rem;
                    padding: 0.5rem 1.5rem;">
            <i class="fas fa-arrow-left me-2"></i>Back
          </a>
          <div>
            <button type="button" class="btn me-2" data-bs-toggle="modal" data-bs-target="#deleteConfirmModal"
                    style="background: rgba(231, 74, 59, 0.1);
                           color: #e74a3b;
                           border: 1px solid rgba(231, 74, 59, 0.2);
                           border-radius: 0.75rem;
                           padding: 0.5rem 1.5rem;">
              <i class="fas fa-trash me-2"></i>Delete
            </button>
            <button type="submit" class="btn"
                    style="background: rgba(28, 200, 138, 0.1);
                           color: #1cc88a;
                           border: 1px solid rgba(28, 200, 138, 0.2);
                           border-radius: 0.75rem;
                           padding: 0.5rem 1.5rem;">
              <i class="fas fa-save me-2"></i>Save Changes
            </button>
          </div>
        </div>
      </form>
        </div>
      </div>
    </div>

    <!-- Tips Column -->
    <div class="col-xl-4 col-lg-5">
      <div class="card" style="border: 1px solid rgba(28, 200, 138, 0.2);
                  border-radius: 1rem !important;
                  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
                  position: sticky;
                  top: 1rem;">
        <div class="card-header bg-white d-flex justify-content-between align-items-center p-4"
             style="border-bottom: 1px solid rgba(28, 200, 138, 0.2);
                    border-top-left-radius: 1rem !important;
                    border-top-right-radius: 1rem !important;">
          <h5 class="text-success m-0">Calendar Event Tips</h5>
        </div>
        <div class="card-body p-4">
          <p class="text-muted small mb-3">Here are some helpful tips for creating effective calendar events:</p>

          <div class="mb-3">
            <h6 class="text-dark"><i class="fas fa-clock text-success me-2"></i>Time Management</h6>
            <p class="small text-muted">Use the all-day option for events that don't have specific start and end times. For timed events, be precise with your scheduling.</p>
          </div>

          <div class="mb-3">
            <h6 class="text-dark"><i class="fas fa-sync-alt text-success me-2"></i>Recurring Events</h6>
            <p class="small text-muted">Set up recurring events for regular activities like watering, fertilizing, or harvesting. This saves time and ensures consistency.</p>
          </div>

          <div class="mb-3">
            <h6 class="text-dark"><i class="fas fa-leaf text-success me-2"></i>Garden & Zone Selection</h6>
            <p class="small text-muted">Link events to specific gardens, zones, and plants to keep your garden management organized and track plant-specific activities.</p>
          </div>

          <div class="mb-3">
            <h6 class="text-dark"><i class="fas fa-bell text-success me-2"></i>Alerts</h6>
            <p class="small text-muted">Set alerts to remind you of important garden tasks. Choose an appropriate time before the event based on the task's urgency.</p>
          </div>

          <div class="mb-3">
            <h6 class="text-dark"><i class="fas fa-users text-success me-2"></i>Invitees</h6>
            <p class="small text-muted">Add email addresses of people you want to invite to your garden events. This is great for community gardens or collaborative projects.</p>
          </div>

          <div class="mb-4">
            <h6 class="text-dark"><i class="fas fa-palette text-success me-2"></i>Event Types</h6>
            <p class="small text-muted mb-3">Different event types are color-coded for easy identification:</p>

            <div class="event-type-container p-3" style="background-color: rgba(248, 249, 252, 0.7); border-radius: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.04);">
              <!-- Work -->
              <div class="d-flex align-items-center mb-2 p-2" style="border-radius: 8px; transition: all 0.2s ease; background-color: rgba(28, 200, 138, 0.05); border-left: 3px solid #1cc88a;">
                <div class="color-swatch me-3" style="display: inline-block; width: 18px; height: 18px; background-color: #1cc88a; border-radius: 4px; box-shadow: 0 2px 4px rgba(28, 200, 138, 0.3);"></div>
                <span class="small fw-medium">Work</span>
              </div>

              <!-- Community -->
              <div class="d-flex align-items-center mb-2 p-2" style="border-radius: 8px; transition: all 0.2s ease; background-color: rgba(78, 115, 223, 0.05); border-left: 3px solid #4e73df;">
                <div class="color-swatch me-3" style="display: inline-block; width: 18px; height: 18px; background-color: #4e73df; border-radius: 4px; box-shadow: 0 2px 4px rgba(78, 115, 223, 0.3);"></div>
                <span class="small fw-medium">Community</span>
              </div>

              <!-- School -->
              <div class="d-flex align-items-center mb-2 p-2" style="border-radius: 8px; transition: all 0.2s ease; background-color: rgba(246, 194, 62, 0.05); border-left: 3px solid #f6c23e;">
                <div class="color-swatch me-3" style="display: inline-block; width: 18px; height: 18px; background-color: #f6c23e; border-radius: 4px; box-shadow: 0 2px 4px rgba(246, 194, 62, 0.3);"></div>
                <span class="small fw-medium">School</span>
              </div>

              <!-- Personal -->
              <div class="d-flex align-items-center mb-2 p-2" style="border-radius: 8px; transition: all 0.2s ease; background-color: rgba(231, 74, 59, 0.05); border-left: 3px solid #e74a3b;">
                <div class="color-swatch me-3" style="display: inline-block; width: 18px; height: 18px; background-color: #e74a3b; border-radius: 4px; box-shadow: 0 2px 4px rgba(231, 74, 59, 0.3);"></div>
                <span class="small fw-medium">Personal</span>
              </div>

              <!-- TODO Task -->
              <div class="d-flex align-items-center p-2" style="border-radius: 8px; transition: all 0.2s ease; background-color: rgba(54, 185, 204, 0.05); border-left: 3px solid #36b9cc;">
                <div class="color-swatch me-3" style="display: inline-block; width: 18px; height: 18px; background-color: #36b9cc; border-radius: 4px; box-shadow: 0 2px 4px rgba(54, 185, 204, 0.3);"></div>
                <span class="small fw-medium">TODO Task</span>
              </div>
            </div>
          </div>
        </div>
            <!-- Tips Column -->

      </div>
    </div>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content" style="border-radius: 15px;">
      <div class="modal-header border-0">
        <h5 class="modal-title" id="deleteConfirmModalLabel" style="color: #e74a3b;">
          <i class="fas fa-exclamation-triangle me-2"></i>Confirm Deletion
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        Are you sure you want to delete this event? This action cannot be undone.
      </div>
      <div class="modal-footer border-0">
        <button type="button" class="btn" data-bs-dismiss="modal"
                style="background: rgba(133, 135, 150, 0.1);
                       color: #858796;
                       border: 1px solid rgba(133, 135, 150, 0.2);
                       border-radius: 0.75rem;
                       padding: 0.5rem 1.5rem;">
          Cancel
        </button>
        <form action="{{ url_for('main.delete_event', event_id=event.id) }}" method="POST" class="d-inline">
          <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
          <button type="submit" class="btn ms-2"
                  style="background: rgba(231, 74, 59, 0.1);
                         color: #e74a3b;
                         border: 1px solid rgba(231, 74, 59, 0.2);
                         border-radius: 0.75rem;
                         padding: 0.5rem 1.5rem;">
            <i class="fas fa-trash me-2"></i>Delete Event
          </button>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Add styles -->
<style>
  .modal-dialog {
    max-width: 500px;
  }

  .form-group label {
    color: #5a5c69;
    font-weight: 500;
  }

  .input-group-text {
    background-color: transparent;
    border-right: none;
  }

  .input-group .form-control {
    border-left: none;
  }

  .form-control:focus {
    border-color: #1cc88a;
    box-shadow: 0 0 0 0.2rem rgba(28, 200, 138, 0.25);
  }

  .form-select:focus {
    border-color: #1cc88a;
    box-shadow: 0 0 0 0.2rem rgba(28, 200, 138, 0.25);
  }

  .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
  }

  .form-label {
    color: #5a5c69;
    font-weight: 500;
    margin-bottom: 0.5rem;
  }

  .modal-content {
    border: none;
  }

  .modal-header, .modal-footer {
    border: none;
  }
</style>

<!-- Add this JavaScript -->
<script>
  document.addEventListener("DOMContentLoaded", function () {
    // Handle all-day event toggle
    const allDayCheckbox = document.getElementById("allDayEvent");
    const startTimeContainer = document.getElementById("startTimeContainer");
    const endTimeContainer = document.getElementById("endTimeContainer");

    allDayCheckbox.addEventListener("change", function () {
      startTimeContainer.style.display = this.checked ? "none" : "block";
      endTimeContainer.style.display = this.checked ? "none" : "block";
    });

    // Handle repeat option changes
    const repeatOption = document.getElementById("repeatOption");
    const endRepeatContainer = document.getElementById("endRepeatContainer");
    const endRepeatSelect = document.getElementById("endRepeat");
    const endRepeatDate = document.getElementById("endRepeatDate");

    repeatOption.addEventListener("change", function () {
      endRepeatContainer.style.display =
        this.value === "none" ? "none" : "flex";
    });

    endRepeatSelect.addEventListener("change", function() {
      endRepeatDate.style.display = this.value === "on" ? "block" : "none";
    });

    // Initialize zones based on selected garden
    if (document.getElementById('garden_id').value) {
      updateZones();
    }

    // Initialize plants based on selected zone
    if (document.getElementById('zone_id').value) {
      updatePlants();
    }

    // Fix for modal backdrop issue
    const fixBackdropIssue = () => {
      // Remove any lingering backdrops
      const backdrops = document.querySelectorAll('.modal-backdrop');
      backdrops.forEach(backdrop => {
        backdrop.remove();
      });
      // Remove modal-open class from body
      document.body.classList.remove('modal-open');
      document.body.style.overflow = '';
      document.body.style.paddingRight = '';
    };

    // Add event listeners to all modal close buttons
    document.querySelectorAll('[data-bs-dismiss="modal"]').forEach(button => {
      button.addEventListener('click', fixBackdropIssue);
    });

    // Handle ESC key to close modals
    document.addEventListener('keydown', function(event) {
      if (event.key === 'Escape') {
        fixBackdropIssue();
      }
    });

    // Initialize modals with proper handling
    const deleteConfirmModal = document.getElementById('deleteConfirmModal');
    if (deleteConfirmModal) {
      deleteConfirmModal.addEventListener('hidden.bs.modal', fixBackdropIssue);
    }
  });

  // Function to update zones based on selected garden
  function updateZones() {
    const gardenId = document.getElementById('garden_id').value;
    const zoneSelect = document.getElementById('zone_id');

    // Clear current options
    zoneSelect.innerHTML = '<option value="">None</option>';

    if (!gardenId) {
      return;
    }

    // Fetch zones for the selected garden
    fetch(`/api/gardens/${gardenId}/zones`)
      .then(response => response.json())
      .then(zones => {
        zones.forEach(zone => {
          const option = document.createElement('option');
          option.value = zone.id;
          option.textContent = zone.name;
          zoneSelect.appendChild(option);
        });
      })
      .catch(error => console.error('Error fetching zones:', error));
  }

  // Function to update plants based on selected zone
  function updatePlants() {
    const zoneId = document.getElementById('zone_id').value;
    const plantSelect = document.getElementById('plant_id');

    // Hide all plant options first
    Array.from(plantSelect.options).forEach(option => {
      if (option.value === '') {
        // Keep the 'None' option visible
        option.style.display = '';
      } else {
        // Hide all other options initially
        option.style.display = 'none';
      }
    });

    if (!zoneId) {
      return;
    }

    // Show only plants from the selected zone
    Array.from(plantSelect.options).forEach(option => {
      if (option.dataset.zone === zoneId) {
        option.style.display = '';
      }
    });
  }

  // Handle custom event type selection
  document.addEventListener("DOMContentLoaded", function() {
    const calendarSelect = document.getElementById("calendar");
    const customEventTypeContainer = document.getElementById("customEventTypeContainer");
    const eventTypeSelect = document.getElementById("eventTypeId");

    // Function to load custom event types
    function loadCustomEventTypes() {
      fetch('/api/event-types')
        .then(response => response.json())
        .then(eventTypes => {
          // Clear current options
          eventTypeSelect.innerHTML = '<option value="" selected disabled>Select a custom event type</option>';

          // Add options for each event type
          eventTypes.forEach(eventType => {
            const option = document.createElement('option');
            option.value = eventType.id;
            option.textContent = eventType.name;
            option.style.color = eventType.color;

            // Select the current event type if it matches
            {% if event.event_type_id %}
            if (eventType.id == {{ event.event_type_id }}) {
              option.selected = true;
            }
            {% endif %}

            eventTypeSelect.appendChild(option);
          });

          // If no custom event types, show a message
          if (eventTypes.length === 0) {
            const option = document.createElement('option');
            option.value = "";
            option.textContent = "No custom event types available";
            option.disabled = true;
            eventTypeSelect.appendChild(option);
          }
        })
        .catch(error => console.error('Error loading custom event types:', error));
    }

    // Toggle custom event type container based on calendar selection
    calendarSelect.addEventListener('change', function() {
      if (calendarSelect.value === 'custom') {
        customEventTypeContainer.style.display = 'block';
        loadCustomEventTypes();
      } else {
        customEventTypeContainer.style.display = 'none';
      }
    });

    // Load custom event types if calendar type is 'custom'
    if (calendarSelect.value === 'custom') {
      loadCustomEventTypes();
    }
  });
</script>
{% endblock content %}
