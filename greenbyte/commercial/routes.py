from flask import render_template, flash, redirect, url_for, request, jsonify
from greenbyte import db
from greenbyte.commercial import commercial
from greenbyte.models import Client, Order, OrderItem, Delivery, Payment, Farm
from flask_login import current_user, login_required
import json
from datetime import datetime

@commercial.route("/commercial/dashboard")
@login_required
def dashboard():
    """Commercial Dashboard page."""
    return render_template('commercial/dashboard.html', title='Commercial Dashboard')

@commercial.route("/commercial/clients")
@login_required
def clients():
    """Client Management page."""
    return render_template('commercial/clients_improved.html', title='Client Management')

@commercial.route("/commercial/products")
@login_required
def products():
    """Product Management page."""
    return render_template('commercial/products.html', title='Product Management')

@commercial.route("/commercial/test")
@login_required
def test():
    """Test page for jQuery."""
    return render_template('commercial/test.html', title='jQuery Test')

@commercial.route("/commercial/orders")
@login_required
def orders():
    """Order Management page."""
    return render_template('commercial/orders.html', title='Order Management')

@commercial.route("/commercial/deliveries")
@login_required
def deliveries():
    """Delivery Management page."""
    return render_template('commercial/deliveries.html', title='Delivery Management')

@commercial.route("/commercial/invoices")
@login_required
def invoices():
    """Invoice Management page."""
    return render_template('commercial/invoices.html', title='Invoice Management')

@commercial.route("/commercial/farm_hub")
@login_required
def farm_hub():
    """Farm Hub page."""
    # Get the user's farms or selected farm
    selected_farm = None
    farm_id = request.args.get('farm_id', None, type=int)

    if farm_id:
        selected_farm = Farm.query.get_or_404(farm_id)
    elif current_user.is_authenticated:
        # Try to get the first farm the user is associated with
        farms = Farm.query.filter_by(owner_id=current_user.id).all()
        if farms:
            selected_farm = farms[0]

    return render_template('commercial/page_farm_hub.html',
                           title='Farm Hub',
                           selected_farm=selected_farm)

@commercial.route("/commercial/create_farm", methods=['GET', 'POST'])
@login_required
def create_farm():
    """Create a new farm."""
    from greenbyte.commercial.forms import FarmForm
    form = FarmForm()

    if form.validate_on_submit():
        farm = Farm(
            name=form.name.data,
            location=form.location.data,
            description=form.description.data,
            business_name=form.business_name.data,
            tax_id=form.tax_id.data,
            phone=form.phone.data,
            email=form.email.data,
            website=form.website.data,
            owner_id=current_user.id
        )
        db.session.add(farm)
        db.session.commit()
        flash('Your farm has been created!', 'success')
        return redirect(url_for('commercial.farm_hub', farm_id=farm.id))

    return render_template('commercial/add_farm.html',
                           title='Create Farm',
                           form=form)

@commercial.route("/commercial/farm/<int:farm_id>/edit", methods=['GET', 'POST'])
@login_required
def edit_farm(farm_id):
    """Edit an existing farm."""
    farm = Farm.query.get_or_404(farm_id)

    # Check if user is authorized to edit this farm
    if farm.owner_id != current_user.id and not current_user.is_super_user():
        flash('You do not have permission to edit this farm.', 'danger')
        return redirect(url_for('commercial.farm_hub'))

    from greenbyte.commercial.forms import FarmForm
    form = FarmForm()

    if form.validate_on_submit():
        farm.name = form.name.data
        farm.location = form.location.data
        farm.description = form.description.data
        farm.business_name = form.business_name.data
        farm.tax_id = form.tax_id.data
        farm.phone = form.phone.data
        farm.email = form.email.data
        farm.website = form.website.data
        db.session.commit()
        flash('Your farm has been updated!', 'success')
        return redirect(url_for('commercial.farm_hub', farm_id=farm.id))
    elif request.method == 'GET':
        form.name.data = farm.name
        form.location.data = farm.location
        form.description.data = farm.description
        form.business_name.data = farm.business_name
        form.tax_id.data = farm.tax_id
        form.phone.data = farm.phone
        form.email.data = farm.email
        form.website.data = farm.website

    return render_template('commercial/edit_farm.html',
                           title='Edit Farm',
                           form=form,
                           farm=farm)

@commercial.route("/commercial/farm/<int:farm_id>/add_member", methods=['GET', 'POST'])
@login_required
def add_farm_member(farm_id):
    """Add a member to a farm."""
    farm = Farm.query.get_or_404(farm_id)

    # Check if user is authorized to add members to this farm
    if farm.owner_id != current_user.id and not current_user.is_super_user():
        flash('You do not have permission to add members to this farm.', 'danger')
        return redirect(url_for('commercial.farm_hub'))

    # Placeholder for now - would need a proper form and user selection
    return render_template('commercial/add_farm_member.html',
                           title='Add Farm Member',
                           farm=farm)
