/* Custom calendar styles to increase height */
.column-content {
    min-height: 600px; /* Increased from 300px to 600px */
    padding: 0 5px;
}

/* Make the calendar responsive */
@media (max-width: 1200px) {
    .column-content {
        min-height: 500px;
    }
}

@media (max-width: 992px) {
    .column-content {
        min-height: 400px;
    }
}

/* Add scrolling for small screens */
@media (max-width: 768px) {
    .column-content {
        min-height: 350px;
        max-height: 500px;
        overflow-y: auto;
    }
}

/* Improve event card styling */
.event-card {
    margin-bottom: 10px; /* Slightly increased from 8px */
    transition: all 0.2s ease;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.08);
}

.event-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.12);
}

.event-title {
    font-size: 0.9rem;
    line-height: 1.3;
    margin-bottom: 4px;
    word-break: break-word;
}

.event-time {
    font-size: 0.75rem !important;
    color: #6c757d !important;
}

/* Ensure the calendar container expands properly */
.weekly-board {
    height: 100%;
}

.board-columns {
    height: 100%;
}

.board-column {
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* Make the column header sticky for better usability when scrolling */
.column-header {
    position: sticky;
    top: 0;
    background-color: white;
    z-index: 10;
    padding: 12px 0;
    margin-bottom: 12px;
    border-bottom: 1px solid rgba(28, 200, 138, 0.2);
}

.day-name {
    font-weight: 600;
    color: #5a5c69;
}

.day-date {
    font-size: 0.85rem;
    color: #858796;
}

/* Improve multi-day event styling */
.multi-day-event {
    background-color: rgba(28, 200, 138, 0.08) !important;
    border-left: 3px solid #1cc88a !important;
}

/* Clean background for the calendar */
.card-body {
    background-color: #ffffff;
}
