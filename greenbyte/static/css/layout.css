/* Layout Styles */

body {
  background: #f8f9fc;
  overflow-x: hidden !important;
  max-width: 100% !important;
  width: 100% !important;
}

/* Custom card styling */
.custom-card {
  border: 1px solid rgba(28, 200, 138, 0.2) !important;
  border-radius: 1.5rem !important;
  border-bottom: 4px solid rgba(28, 200, 138, 0.2) !important;
  background: white !important;
  box-shadow: 0 8px 24px rgba(0,0,0,0.12) !important;
}

/* Floating sidebar styles */
.sidebar {
  width: 80px !important;
  height: calc(100vh - 2rem);
  position: fixed;
  top: 1rem;
  left: 1rem;
  z-index: 1030;
  background: white;
  border-radius: 1.5rem;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(28, 200, 138, 0.2);
  border-bottom: 4px solid rgba(28, 200, 138, 0.2);
}

/* Hide all text by default */
.sidebar span {
  display: none;
}

/* Center icons */
.sidebar .nav-link {
  text-align: center;
  padding: 1rem;
  position: relative;
  color: #1cc88a;
  text-decoration: none;
  transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
  color: #169b6b;
}

.sidebar .nav-link i {
  font-size: 1.2rem;
}

/* Tooltip styles on hover */
.sidebar .nav-link:hover span {
  display: block;
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background: #1cc88a;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.75rem;
  margin-left: 10px;
  white-space: nowrap;
  z-index: 1000;
  box-shadow: 0 4px 6px rgba(28, 200, 138, 0.1);
}

/* Content wrapper adjustment */
#content-wrapper {
  margin-left: 96px;
  padding: 1rem;
  transition: margin-left 0.3s ease;
}

/* Divider styles */
.sidebar-divider {
  border: none;
  border-top: 1px solid rgba(28, 200, 138, 0.1);
  margin: 0.5rem 1rem;
}

.commercial-divider {
  height: 1px;
  background-color: rgba(255,255,255,0.1);
}

/* Hamburger menu button */
.hamburger-menu {
  display: none; /* Hidden by default on desktop */
  position: fixed;
  top: 1rem;
  left: 1rem;
  z-index: 1040;
  background-color: white;
  border: 1px solid rgba(28, 200, 138, 0.2);
  border-radius: 0.25rem;
  width: 40px;
  height: 40px;
  font-size: 1.2rem;
  color: #1cc88a;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.hamburger-menu:hover {
  color: #19a97c;
  background-color: #f8f9fc;
}

/* Sidebar overlay */
#sidebar-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1025;
  backdrop-filter: blur(2px);
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* Mobile responsive styles */
@media (max-width: 768px) {
  /* Ensure nav links have proper padding */
  .sidebar .nav-link {
    padding: 1rem !important;
  }

  /* Ensure logo and user sections have proper padding */
  .logo-section {
    padding: 1.5rem 0 !important;
  }

  .user-section {
    padding: 1.5rem 0 !important;
    margin-top: auto !important;
  }

  /* Custom scrollbar for sidebar */
  .sidebar::-webkit-scrollbar {
    width: 6px;
  }

  .sidebar::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 10px;
  }

  .sidebar::-webkit-scrollbar-thumb {
    background: rgba(28, 200, 138, 0.3);
    border-radius: 10px;
  }

  .sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(28, 200, 138, 0.5);
  }
  .hamburger-menu {
    display: flex !important; /* Always show on mobile with !important to override */
    align-items: center;
    justify-content: center;
    top: 0.75rem;
    left: 0.75rem;
  }

  /* Make the sidebar overlay the content on mobile */
  .sidebar {
    position: fixed !important;
    top: 1rem !important;
    left: 1rem !important;
    height: calc(100vh - 2rem) !important;
    max-height: calc(100vh - 2rem) !important;
    overflow-y: auto !important;
    z-index: 1030 !important;
    display: none !important; /* Hide by default on mobile */
    transition: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
    border-radius: 1.5rem !important;
    margin: 0 !important;
    padding: 1rem 0 !important;
    background-color: white !important;
    border: 1px solid rgba(28, 200, 138, 0.2) !important;
    border-bottom: 4px solid rgba(28, 200, 138, 0.2) !important;
    flex-direction: column !important;
  }

  /* When sidebar is toggled */
  body.sidebar-toggled .sidebar {
    display: flex !important; /* Show as flex to maintain column layout */
  }

  body.sidebar-toggled .hamburger-menu {
    display: none !important; /* Hide hamburger when sidebar is visible with !important to override */
  }

  body.sidebar-toggled #sidebar-overlay {
    display: block;
    opacity: 1;
  }

  /* Adjust main content for mobile */
  #content-wrapper {
    margin-left: 0 !important; /* Remove margin for sidebar */
    width: 100% !important;
    max-width: 100% !important;
    padding: 0 !important;
    overflow-x: hidden !important;
  }

  #content {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0 !important;
    overflow-x: hidden !important;
  }

  .container-fluid {
    padding-left: 0.25rem !important;
    padding-right: 0.25rem !important;
    max-width: 100% !important;
    width: 100% !important;
    margin: 0 !important;
    overflow-x: hidden !important;
  }

  /* Center content in single column */
  .row {
    justify-content: flex-start !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
    padding: 0 !important;
    overflow-x: hidden !important;
  }

  /* Expand cards to use more space */
  .card {
    margin-bottom: 1rem !important;
    width: 100% !important;
    max-width: 100% !important;
    border-radius: 1rem !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    overflow-x: hidden !important;
  }

  /* Make content use more of the screen width */
  .col-xl-8, .col-lg-7, .col-md-6, .col-sm-12 {
    padding-left: 0.1rem !important;
    padding-right: 0.1rem !important;
    max-width: 100% !important;
    width: 100% !important;
    flex: 0 0 100% !important;
    overflow-x: hidden !important;
  }

  /* Adjust right column cards */
  .col-xl-4, .col-lg-5, .col-md-6 {
    padding-left: 0.1rem !important;
    padding-right: 0.1rem !important;
    max-width: 100% !important;
    width: 100% !important;
    flex: 0 0 100% !important;
    overflow-x: hidden !important;
  }
}

/* Logo section styles */
.logo-section {
  padding: 1.5rem 0;
  text-align: center;
}

.logo-section i {
  color: #1cc88a;
  font-size: 1.5rem;
}

/* Navigation section */
.nav-section {
  flex: 1;
  padding: 0.5rem 0;
}

/* User section styles */
.user-section {
  padding: 1rem 0;
  text-align: center;
  margin-top: auto;
}

.user-btn {
  background: none;
  border: none;
  color: #1cc88a;
  width: 100%;
  padding: 0.5rem;
  cursor: pointer;
  position: relative;
}

.user-btn:hover {
  color: #169b6b;
}

/* Dropdown Styles */
.user-dropdown {
  display: none;
  position: absolute;
  left: 100%;
  bottom: 0;
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 0.15rem 1.75rem rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(28, 200, 138, 0.1);
  min-width: 180px;
  z-index: 1000;
  margin-left: 10px;
}

.user-dropdown.show {
  display: block;
}

/* Nav Dropdown Styles */
.sidebar .dropdown-menu {
  position: absolute;
  left: 100%;
  top: 0;
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 0.15rem 1.75rem rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(28, 200, 138, 0.1);
  min-width: 180px;
  z-index: 1000;
  margin-left: 10px;
  padding: 0.5rem 0;
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  color: #3a3b45;
  text-decoration: none;
  font-size: 0.85rem;
  white-space: nowrap;
}

.dropdown-item:hover {
  background-color: #f8f9fc;
  color: #1cc88a;
}

.dropdown-item i {
  color: #1cc88a;
  margin-right: 0.5rem;
  width: 1rem;
  text-align: center;
}

.dropdown-divider {
  border-top: 1px solid rgba(28, 200, 138, 0.1);
  margin: 0.5rem 0;
}

/* Dropdown toggle icon */
.sidebar .dropdown-toggle::after {
  margin-left: auto;
  vertical-align: middle;
}

/* Flash Messages */
#flash-messages {
  z-index: 1050;
}

/* Footer Styles */
.sticky-footer {
  padding: 1rem 0;
  margin-top: 2rem;
  text-align: center;
  border-top: 1px solid rgba(28, 200, 138, 0.2);
}

.sticky-footer .text-center {
  color: #858796;
  font-size: 0.8rem;
}

/* Background Styles */
body {
  background: url('https://images.unsplash.com/photo-1472214103451-9374bd1c798e?auto=format&fit=crop&w=1920&q=80');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  background-repeat: no-repeat;
  position: relative;
  min-height: 100vh;
}

/* Overlay */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #f8f9fc;
  backdrop-filter: blur(5px);
  z-index: -1;
}

/* Content Wrapper Adjustments */
#content-wrapper {
  margin-left: calc(80px + 1.5rem);
  padding: 0;
  max-width: calc(100% - (80px + 1.5rem));
  margin-top: 0.5rem !important;
  padding: 0 !important;
}

/* Clean, static card styling */
.card {
  border: 1px solid rgba(28, 200, 138, 0.2);
  border-radius: 1.5rem !important;
  border-bottom: 4px solid rgba(28, 200, 138, 0.2);
  background: white;
  margin-bottom: 1rem;
  box-shadow: 0 8px 24px rgba(0,0,0,0.12) !important;
}

/* Basic card header */
.card-header {
  padding: 0.75rem;
  background-color: transparent;
  border-bottom: 1px solid #e3e6f0;
  border-top-left-radius: 1.5rem !important;
  border-top-right-radius: 1.5rem !important;
}

/* Simple card body */
.card-body {
  padding: 0.75rem;
}

/* Card footer */
.card-footer {
  padding: 0.75rem;
  background-color: transparent;
  border-top: 1px solid #e3e6f0;
  border-bottom-left-radius: 1.5rem !important;
  border-bottom-right-radius: 1.5rem !important;
}

/* Basic typography */
.card-title {
  font-size: 1rem;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.card-text {
  font-size: 0.875rem;
  color: #6c757d;
}

/* Remove all transitions and hover states */
.card,
.card-header,
.card-body,
.card-footer {
  transition: none;
}

/* Reset any bootstrap shadow classes with neutral shadows */
.shadow {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow-lg {
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

/* Compact grid spacing */
.row>[class*="col-"] {
  padding: 0.5rem;
}

/* Container fluid alignment */
.container-fluid {
  padding: 1rem !important;
  padding-top: 0 !important;
  margin: 0 !important;
}

/* Additional responsive adjustments */
@media (max-width: 768px) {
  #content-wrapper {
    margin-left: calc(80px + 1rem);
    max-width: calc(100% - (80px + 1rem));
  }
}

@media (max-width: 480px) {
  #content-wrapper {
    margin-left: 1rem;
    max-width: 100%;
  }
}

/* Toast Styles */
#toast-container, .share-toast-container {
  z-index: 1100;
}

.toast, .share-toast {
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
}

.toast-success .toast-header, .share-toast .toast-header {
  background-color: rgba(19, 141, 97, 0.95);
  color: white;
}

.toast-danger .toast-header {
  background-color: rgba(231, 74, 59, 0.95);
  color: white;
}

.toast-warning .toast-header {
  background-color: rgba(246, 194, 62, 0.95);
  color: white;
}

.toast-info .toast-header {
  background-color: rgba(54, 185, 204, 0.95);
  color: white;
}

.toast .toast-body, .share-toast .toast-body {
  background-color: rgba(255, 255, 255, 0.7);
}

.share-toast .form-control {
  background-color: rgba(255, 255, 255, 0.8);
}

.share-toast .btn-outline-success {
  background-color: rgba(255, 255, 255, 0.8);
}
