/* Gardens Page Styles */

/* Custom scrollbar styling */
.right-column-container::-webkit-scrollbar {
    width: 8px;
}

.right-column-container::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 10px;
}

.right-column-container::-webkit-scrollbar-thumb {
    background: rgba(28, 200, 138, 0.3);
    border-radius: 10px;
}

.right-column-container::-webkit-scrollbar-thumb:hover {
    background: rgba(28, 200, 138, 0.5);
}

/* Ensure dropdowns appear on top of other content */
.dropdown-menu {
    z-index: 9999 !important;
    position: fixed !important;
    transform: none !important;
}

/* Garden Card Styles */
.garden-card {
    border: 1px solid rgba(28, 200, 138, 0.2);
    border-radius: 1rem !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    width: 100%;
}

.garden-card-header {
    border-bottom: 1px solid rgba(28, 200, 138, 0.2);
    border-radius: 1rem !important;
}

.garden-content {
    border-bottom-left-radius: 1rem;
    border-bottom-right-radius: 1rem;
}

/* Garden Button Styles */
.garden-btn {
    background: rgba(28, 200, 138, 0.1);
    color: #1cc88a;
    border: 1px solid rgba(28, 200, 138, 0.2);
}

.garden-btn-rounded {
    background: rgba(28, 200, 138, 0.1);
    color: #1cc88a;
    border: 1px solid rgba(28, 200, 138, 0.2);
    border-radius: 0.5rem;
    transition: all 0.2s ease;
}

/* Garden Overview Card Styles */
.garden-overview-card {
    background: white;
    border: 1px solid rgba(28, 200, 138, 0.2);
    border-radius: 1rem !important;
    border-bottom: 4px solid rgba(28, 200, 138, 0.2);
}

.status-item {
    border: 1px dashed #1cc88a;
}

/* Right Column Container Styles */
.right-column-container {
    position: sticky;
    top: 1rem;
    max-height: calc(100vh - 2rem);
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(28, 200, 138, 0.3) rgba(0, 0, 0, 0.05);
}

/* Mobile responsiveness for garden cards */
@media (max-width: 768px) {
    .card,
    .plants-container,
    .zone-card {
        width: 100% !important;
        max-width: 100% !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        padding-left: 0 !important;
        padding-right: 0 !important;
        border-radius: 0.5rem !important;
        overflow-x: hidden !important;
    }

    .plants-container {
        overflow-x: auto !important;
    }

    /* Ensure table headers and rows have consistent width */
    .plant-table-header,
    .plant-row {
        min-width: 650px !important;
        width: 100% !important;
    }

    .right-column-container {
        position: static !important;
        max-height: none !important;
        overflow-y: visible !important;
        width: 100% !important;
        max-width: 100% !important;
        overflow-x: hidden !important;
    }

    .col-12 {
        padding-left: 0 !important;
        padding-right: 0 !important;
        width: 100% !important;
        max-width: 100% !important;
        overflow-x: hidden !important;
    }

    body {
        overflow-x: hidden !important;
    }

    /* Additional dropdown styling for mobile */
    .dropdown-menu {
        background-color: white !important;
        border: 1px solid rgba(28, 200, 138, 0.2) !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    }
}
