.list-group-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.05) !important;
    border-color: rgba(28, 200, 138, 0.2) !important;
}

.plant-row:hover {
    background-color: rgba(28, 200, 138, 0.12) !important;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(28, 200, 138, 0.2);
}

.dropdown-menu {
    border: 1px solid rgba(28, 200, 138, 0.1);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.dropdown-item:hover {
    background: rgba(28, 200, 138, 0.1);
    color: #1cc88a;
}

@media (max-width: 768px) {
    .col-md-4, .col-md-3 {
        margin-top: 1rem;
    }
}

/* Calendar styles */
.mini-day-wrapper {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.mini-day {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.mini-day:hover {
    background-color: rgba(28, 200, 138, 0.1);
    cursor: pointer;
}

.event-indicator {
    width: 5px;
    height: 5px;
    border-radius: 50%;
    background-color: #1cc88a;
    margin-top: 2px;
    opacity: 0;
    transition: opacity 0.2s;
}

.event-indicator.has-event {
    opacity: 1;
}
