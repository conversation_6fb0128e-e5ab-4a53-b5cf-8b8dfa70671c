/* Zone Card Styles */

/* Zone Card */
#zone-card {
  width: 100%;
}

/* Zone Card Header */
.zone-card-header {
  border-radius: 0.75rem !important;
  background-color: rgba(28, 200, 138, 0.05);
  border-bottom: 1px solid rgba(28, 200, 138, 0.1);
}

/* Zone Header Title */
.zone-header {
  color: #1cc88a;
  font-size: 1.1rem;
  font-weight: 600;
  letter-spacing: 0.5px;
}

/* Zone Icon */
.zone-icon {
  color: rgba(28, 200, 138, 0.6);
}

/* Zone Badge */
.zone-badge {
  font-size: 0.7rem;
}

/* Zone Action Buttons */
.zone-btn {
  background: rgba(28, 200, 138, 0.1);
  color: #1cc88a;
  border: 1px solid rgba(28, 200, 138, 0.2);
}

.zone-btn-text {
  font-size: 0.8rem;
}

.zone-todo-btn {
  background: rgba(54, 185, 204, 0.1);
  color: #36b9cc;
  border: 1px solid rgba(54, 185, 204, 0.2);
  font-size: 0.8rem;
}

/* Status Flow */
.status-flow {
  border-color: rgba(28, 200, 138, 0.2) !important;
}

.status-flow-container {
  scrollbar-width: thin;
}

/* Plant Table Header */
.plant-table-header {
  background-color: rgba(28, 200, 138, 0.05);
  font-weight: 500;
  min-width: 650px;
  width: 100%;
}

/* Plant Row */
.plant-row {
  border-color: rgba(28, 200, 138, 0.1) !important;
  min-width: 650px;
  transition: background-color 0.2s ease;
  width: 100%;
}

/* Plant Name */
.plant-name {
  color: #1cc88a;
  font-weight: 500;
}

/* Plant Status Badge */
.plant-status-badge {
  font-size: 0.8rem;
  padding: 0.35rem 0.75rem;
  white-space: nowrap;
  max-width: fit-content;
}

/* Dropdown Menus */
.plant-dropdown-menu {
  display: none;
  z-index: 1050;
  position: absolute;
}

/* Plants Container */
.plants-container {
  width: 100%;
  overflow-x: auto;
}
