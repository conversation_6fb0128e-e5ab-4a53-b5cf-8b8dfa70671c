// Todo Card JavaScript

// Function to initialize a TODO checkbox
function initializeTodoCheckbox(checkbox) {
    if (!checkbox) return;

    // Remove any existing event listeners
    const newCheckbox = checkbox.cloneNode(true);
    checkbox.parentNode.replaceChild(newCheckbox, checkbox);

    // Add the event listener
    newCheckbox.addEventListener('change', function () {
        const todoId = this.dataset.todoId || this.dataset.eventId;
        const todoItem = this.closest('.todo-item') || this.closest('.event-item');
        const todoTitle = todoItem.querySelector('.todo-title');

        if (this.checked) {
            // Mark as completed visually
            if (todoTitle) {
                todoTitle.style.textDecoration = 'line-through';
                todoTitle.style.opacity = '0.6';
            }
            todoItem.style.opacity = '0.7';

            // Send AJAX request to mark the event as completed
            fetch(`/event/${todoId}/toggle_complete`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                },
                body: JSON.stringify({ completed: true }),
                credentials: 'same-origin'
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Success - task will remain visible but crossed out until page refresh
                        const todoTitle = todoItem.querySelector('.todo-title')?.textContent || 'Task';
                        window.showToast(`"${todoTitle.trim()}" marked as completed!`, 'bg-success');
                    } else {
                        // Error - revert visual changes
                        this.checked = false;
                        if (todoTitle) {
                            todoTitle.style.textDecoration = 'none';
                            todoTitle.style.opacity = '1';
                        }
                        todoItem.style.opacity = '1';
                        window.showToast(data.message || 'Error completing task', 'bg-danger');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    // Revert visual changes on error
                    this.checked = false;
                    if (todoTitle) {
                        todoTitle.style.textDecoration = 'none';
                        todoTitle.style.opacity = '1';
                    }
                    todoItem.style.opacity = '1';
                    window.showToast('An error occurred while completing the task', 'bg-danger');
                });
        } else {
            // Handle unmarking a task as completed
            if (todoTitle) {
                todoTitle.style.textDecoration = 'none';
                todoTitle.style.opacity = '1';
            }
            todoItem.style.opacity = '1';

            // Send AJAX request to unmark the event as completed
            fetch(`/event/${todoId}/toggle_complete`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                },
                body: JSON.stringify({ completed: false }),
                credentials: 'same-origin'
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Success - task will be unmarked
                        const todoTitle = todoItem.querySelector('.todo-title')?.textContent || 'Task';
                        window.showToast(`"${todoTitle.trim()}" unmarked as completed!`, 'bg-info');
                    } else {
                        // Error - revert visual changes
                        this.checked = true;
                        if (todoTitle) {
                            todoTitle.style.textDecoration = 'line-through';
                            todoTitle.style.opacity = '0.6';
                        }
                        todoItem.style.opacity = '0.7';
                        window.showToast(data.message || 'Error unmarking task', 'bg-danger');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    // Revert visual changes on error
                    this.checked = true;
                    if (todoTitle) {
                        todoTitle.style.textDecoration = 'line-through';
                        todoTitle.style.opacity = '0.6';
                    }
                    todoItem.style.opacity = '0.7';
                    window.showToast('An error occurred while unmarking the task', 'bg-danger');
                });
        }
    });

    return newCheckbox;
}

// Make sure this runs after all other scripts
function initializeTodoCheckboxes() {
    // Load more events functionality
    const loadMoreBtn = document.getElementById('load-more-events');
    if (loadMoreBtn) {
        const hiddenEvents = document.getElementById('hidden-events');
        const eventsList = document.getElementById('events-list');
        const totalEvents = parseInt(hiddenEvents.dataset.totalEvents || 0);
        let currentlyShown = 5;
        const batchSize = 5;

        loadMoreBtn.addEventListener('click', function () {
            // Get the next batch of events
            const nextBatch = Array.from(hiddenEvents.querySelectorAll(`.event-item[data-event-index]`))
                .filter(event => {
                    const index = parseInt(event.dataset.eventIndex);
                    return index > currentlyShown && index <= currentlyShown + batchSize;
                });

            // Clone and append them to the visible list
            nextBatch.forEach(event => {
                const clone = event.cloneNode(true);
                eventsList.appendChild(clone);

                // Add event listeners to checkboxes in the cloned elements
                const checkbox = clone.querySelector('.todo-checkbox');
                if (checkbox) {
                    addTodoCheckboxListener(checkbox);
                }
            });

            // Update counter
            currentlyShown += nextBatch.length;

            // Hide button if we've shown all events
            if (currentlyShown >= totalEvents) {
                loadMoreBtn.parentElement.style.display = 'none';
            }
        });
    }

    // Load more TODOs functionality
    const loadMoreTodosBtn = document.getElementById('load-more-todos');
    if (loadMoreTodosBtn) {
        const hiddenTodos = document.getElementById('hidden-todos');
        const todosList = document.getElementById('todo-list');
        const totalTodos = parseInt(hiddenTodos.dataset.totalTodos || 0);
        let currentlyShownTodos = 5;
        const todoBatchSize = 5;

        loadMoreTodosBtn.addEventListener('click', function () {
            // Get the next batch of todos
            const nextBatch = Array.from(hiddenTodos.querySelectorAll(`.todo-item[data-todo-index]`))
                .filter(todo => {
                    const index = parseInt(todo.dataset.todoIndex);
                    return index > currentlyShownTodos && index <= currentlyShownTodos + todoBatchSize;
                });

            // Clone and append them to the visible list
            nextBatch.forEach(todo => {
                const clone = todo.cloneNode(true);
                todosList.appendChild(clone);

                // Add event listeners to checkboxes in the cloned elements
                const checkbox = clone.querySelector('.todo-checkbox');
                if (checkbox) {
                    addTodoCheckboxListener(checkbox);
                }
            });

            // Update counter
            currentlyShownTodos += nextBatch.length;

            // Hide button if we've shown all todos
            if (currentlyShownTodos >= totalTodos) {
                loadMoreTodosBtn.parentElement.style.display = 'none';
            }
        });
    }

    // Add event listeners to all todo checkboxes
    function addTodoCheckboxListener(checkbox) {
        // Use the initializeTodoCheckbox function to set up the checkbox
        initializeTodoCheckbox(checkbox);
    }

    // Initialize all visible todo checkboxes
    document.querySelectorAll('.todo-checkbox').forEach(checkbox => {
        addTodoCheckboxListener(checkbox);
    });
}

// Initialize todo checkboxes when the DOM is loaded
document.addEventListener('DOMContentLoaded', initializeTodoCheckboxes);

// Also call it now in case the DOM is already loaded
if (document.readyState === 'complete' || document.readyState === 'interactive') {
    setTimeout(initializeTodoCheckboxes, 100);
}

// Make these functions available globally
window.initializeTodoCheckbox = initializeTodoCheckbox;
window.initializeTodoCheckboxes = initializeTodoCheckboxes;
