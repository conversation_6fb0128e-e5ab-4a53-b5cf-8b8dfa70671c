// Gardens Inline JavaScript

// Function to update relative timestamps
function updateRelativeTimestamps() {
    const timestamps = document.querySelectorAll('.relative-timestamp');
    const now = new Date();

    timestamps.forEach(timestamp => {
        const timestampDate = new Date(timestamp.getAttribute('data-timestamp'));
        const timeDiff = Math.floor((now - timestampDate) / 1000); // in seconds

        let timeText = '';
        if (timeDiff < 60) {
            timeText = 'just now';
        } else if (timeDiff < 3600) {
            const mins = Math.floor(timeDiff / 60);
            timeText = `${mins} min${mins !== 1 ? 's' : ''} ago`;
        } else if (timeDiff < 86400) {
            const hours = Math.floor(timeDiff / 3600);
            timeText = `${hours} hour${hours !== 1 ? 's' : ''} ago`;
        } else if (timeDiff < 604800) {
            const days = Math.floor(timeDiff / 86400);
            timeText = `${days} day${days !== 1 ? 's' : ''} ago`;
        } else if (timeDiff < 2592000) {
            const weeks = Math.floor(timeDiff / 604800);
            timeText = `${weeks} week${weeks !== 1 ? 's' : ''} ago`;
        } else {
            // For older timestamps, keep the original date format
            timeText = timestamp.getAttribute('data-original-text');
        }

        timestamp.textContent = timeText;
    });
}

// Update timestamps every minute
setInterval(updateRelativeTimestamps, 60000);

document.addEventListener('DOMContentLoaded', function() {
    // Initialize timestamps when the page loads
    updateRelativeTimestamps();

    // Helper function to show toast notifications
    function showToast(message, bgClass = 'bg-success') {
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white ${bgClass} border-0`;
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');

        const icon = bgClass === 'bg-success' ? 'check-circle' :
                     bgClass === 'bg-danger' ? 'exclamation-circle' : 'info-circle';

        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-${icon} me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        `;

        // Add toast to the document
        const toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.appendChild(toast);
        document.body.appendChild(toastContainer);

        // Initialize and show the toast
        const bsToast = new bootstrap.Toast(toast, {
            autohide: true,
            delay: 5000
        });
        bsToast.show();

        // Remove toast container after toast is hidden
        toast.addEventListener('hidden.bs.toast', function() {
            document.body.removeChild(toastContainer);
        });

        return toast;
    }

    // Function to update plant status - make it globally available
    window.updatePlantStatus = function(plantId, newStatus, csrfToken) {
        alert('updatePlantStatus called! Plant: ' + plantId + ', Status: ' + newStatus);
        console.log('GARDENS_INLINE.JS: Updating plant', plantId, 'to status', newStatus);
        console.log('CSRF token received:', csrfToken);

        // Get CSRF token from meta tag if not provided or if provided token seems invalid
        if (!csrfToken || csrfToken.length < 10) {
            const metaToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
            console.log('Getting CSRF token from meta tag:', metaToken);
            csrfToken = metaToken || csrfToken;
        }

        // Find the plant row and elements to update
        const link = document.querySelector(`a.status-update-link[data-plant-id="${plantId}"][data-status="${newStatus}"]`);
        if (!link) {
            console.error('Could not find status link');
            showToast('Error updating status: Could not find status link', 'bg-danger');
            return;
        }

        const plantRow = link.closest('.plant-row');
        const statusBadge = plantRow.querySelector('.col-3.d-flex .badge');
        const timestampElement = plantRow.querySelector('.col-2.text-muted small');
        const dropdownMenu = plantRow.querySelector('.dropdown-menu');
        const dropdownToggle = plantRow.querySelector('.dropdown-toggle');

        // Show loading indicator
        statusBadge.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Updating...';

        // Show toast notification
        showToast('Updating plant status...', 'bg-info');

        // Send AJAX request
        fetch(`/api/plant/${plantId}/status/${newStatus}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken
            }
        })
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);
            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);
            if (data.success) {
                // Update the status badge with new status
                statusBadge.className = `badge ${data.style.bg} d-inline-flex align-items-center gap-1`;
                statusBadge.style.cssText = `
                    font-size: 0.8rem;
                    padding: 0.35rem 0.75rem;
                    ${data.style.extra_style}
                    white-space: nowrap;
                    max-width: fit-content;
                `;
                statusBadge.innerHTML = `
                    <i class="fas fa-${data.style.icon}"></i>
                    ${data.status}
                `;

                // Update the timestamp
                if (timestampElement && data.timestamp) {
                    timestampElement.innerHTML = '<i class="far fa-clock me-1"></i>just now';
                }

                // Update the dropdown menu
                if (dropdownMenu) {
                    // Update the dropdown toggle text
                    if (dropdownToggle) {
                        dropdownToggle.textContent = data.status;
                    }

                    // Clear the dropdown menu
                    dropdownMenu.innerHTML = '';

                    // Use the valid statuses from the server response
                    const validStatuses = data.valid_statuses || ['Seedling', 'Growing', 'Flowering', 'Fruiting', 'Harvested'];

                    // Add all statuses in order, making the current one non-clickable
                    validStatuses.forEach(status => {
                        const statusItem = document.createElement('li');

                        if (status === data.status) {
                            // Current status - non-clickable with checkmark
                            statusItem.innerHTML = `
                                <span class="dropdown-item py-1 text-muted d-flex align-items-center">
                                    <i class="fas fa-check me-2 text-success"></i>
                                    ${status}
                                </span>
                            `;
                        } else {
                            // Other status - clickable
                            statusItem.innerHTML = `
                                <a class="dropdown-item py-1 status-update-link"
                                   href="#"
                                   data-plant-id="${plantId}"
                                   data-status="${status}"
                                   data-csrf-token="${csrfToken}">
                                    ${status}
                                </a>
                            `;

                            // Add event listener to the new link
                            setTimeout(() => {
                                const newLink = statusItem.querySelector('.status-update-link');
                                if (newLink) {
                                    newLink.addEventListener('click', function(e) {
                                        e.preventDefault();
                                        updatePlantStatus(plantId, status, csrfToken);
                                    });
                                }
                            }, 0);
                        }

                        dropdownMenu.appendChild(statusItem);
                    });
                }

                // Show success message
                showToast(data.message, 'bg-success');
            } else {
                // Show error message and revert to original status
                console.error('Error updating status:', data.error);
                showToast('Error updating status: ' + data.error, 'bg-danger');
            }
        })
        .catch(error => {
            console.error('Fetch error:', error);
            console.error('Error details:', error.message, error.stack);
            showToast('An error occurred while updating the status: ' + error.message, 'bg-danger');
        });
    }

    // Helper function to add event listeners to a plant row
    function addEventListenersToPlantRow(plantRow) {
        console.log('Adding event listeners to plant row', plantRow);

        // Add event listeners to status update links
        const statusLinks = plantRow.querySelectorAll('.status-update-link');
        console.log('Found', statusLinks.length, 'status update links');
        statusLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const plantId = this.getAttribute('data-plant-id');
                const newStatus = this.getAttribute('data-status');
                const csrfToken = this.getAttribute('data-csrf-token');
                updatePlantStatus(plantId, newStatus, csrfToken);
            });
        });

        // Add event listeners to move plant links
        const moveLinks = plantRow.querySelectorAll('.move-plant-link');
        console.log('Found', moveLinks.length, 'move plant links');
        moveLinks.forEach(link => {
            console.log('Adding click listener to move link', link);
            link.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Move link clicked');
                const plantId = this.getAttribute('data-plant-id');
                const zoneId = this.getAttribute('data-zone-id');
                const zoneName = this.getAttribute('data-zone-name');
                const csrfToken = this.getAttribute('data-csrf-token');
                console.log('Moving plant', plantId, 'to zone', zoneId);
                movePlant(plantId, zoneId, zoneName, csrfToken, this.closest('.plant-row'));
            });
        });
    }

    // Make functions available globally
    window.showToast = showToast;
    window.updatePlantStatus = updatePlantStatus;
    window.addEventListenersToPlantRow = addEventListenersToPlantRow;

    // Also make movePlant available globally (will be defined in gardens.js)
    window.movePlant = window.movePlant || function() { console.error('movePlant not yet loaded'); };
});
