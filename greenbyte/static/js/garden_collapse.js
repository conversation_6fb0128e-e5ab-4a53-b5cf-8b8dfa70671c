// Garden Collapse JavaScript

document.addEventListener('DOMContentLoaded', function () {
    // Handle garden collapse icons
    const gardenHeaders = document.querySelectorAll('[data-bs-target^="#garden-content-"]');
    gardenHeaders.forEach(header => {
        const icon = header.querySelector('.garden-collapse-icon');
        const targetId = header.getAttribute('data-bs-target');
        const targetElement = document.querySelector(targetId);

        // Set initial state - collapsed by default
        icon.style.transform = 'rotate(0deg)';

        // Add event listener for collapse events
        targetElement.addEventListener('hide.bs.collapse', function () {
            icon.style.transform = 'rotate(0deg)';
        });

        targetElement.addEventListener('show.bs.collapse', function () {
            icon.style.transform = 'rotate(90deg)';
        });
    });

    // Handle zone collapse icons
    const zoneHeaders = document.querySelectorAll('[data-bs-target^="#zone-content-"]');
    zoneHeaders.forEach(header => {
        const icon = header.querySelector('.zone-collapse-icon');
        const targetId = header.getAttribute('data-bs-target');
        const targetElement = document.querySelector(targetId);

        // Set initial state - collapsed by default
        icon.style.transform = 'rotate(0deg)';

        // Add event listener for collapse events
        targetElement.addEventListener('hide.bs.collapse', function () {
            icon.style.transform = 'rotate(0deg)';
        });

        targetElement.addEventListener('show.bs.collapse', function () {
            icon.style.transform = 'rotate(90deg)';
        });
    });
});
