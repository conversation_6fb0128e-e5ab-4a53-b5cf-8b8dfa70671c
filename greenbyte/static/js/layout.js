// Layout JavaScript

// User dropdown (when logged in)
document.addEventListener('DOMContentLoaded', function() {
    const userBtn = document.getElementById('userDropdownBtn');
    const userDropdown = document.getElementById('userDropdown');

    if (userBtn) {
        userBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            userDropdown.classList.toggle('show');
        });
    }

    // Auth dropdown (when not logged in)
    const authBtn = document.getElementById('authDropdownBtn');
    const authDropdown = document.getElementById('authDropdown');

    if (authBtn) {
        authBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            authDropdown.classList.toggle('show');
        });
    }

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        // Close user dropdown if it exists
        if (userDropdown && !userBtn?.contains(e.target)) {
            if (!userDropdown.contains(e.target)) {
                userDropdown.classList.remove('show');
            }
        }

        // Close auth dropdown if it exists
        if (authDropdown && !authBtn?.contains(e.target)) {
            if (!authDropdown.contains(e.target)) {
                authDropdown.classList.remove('show');
            }
        }
    });

    // Auto-dismiss flash messages after 5 seconds
    const flashMessages = document.querySelectorAll('.alert');
    flashMessages.forEach(function(flash) {
        setTimeout(function() {
            const alert = bootstrap.Alert.getOrCreateInstance(flash);
            alert.close();
        }, 5000);
    });
});

// Window resize handler
window.addEventListener('resize', function() {
    const width = window.innerWidth;
    const body = document.body;
    const sidebar = document.querySelector('.sidebar');

    // Auto collapse on small screens
    if (width <= 768) {
        body.classList.add('sidebar-toggled');
        sidebar.classList.add('toggled');
    } else {
        body.classList.remove('sidebar-toggled');
        sidebar.classList.remove('toggled');
    }

    // Hide completely on very small screens
    if (width <= 480) {
        sidebar.style.overflow = 'hidden';
    } else {
        sidebar.style.overflow = '';
    }
});

// Trigger resize handler on load
window.addEventListener('load', function() {
    window.dispatchEvent(new Event('resize'));
});
