// Sidebar Toggle JavaScript

document.addEventListener('DOMContentLoaded', function() {
  // Ensure sidebar is hidden by default on mobile
  if (window.innerWidth <= 768) {
    document.body.classList.remove('sidebar-toggled');
  }

  // Sidebar toggle button
  const sidebarToggle = document.getElementById('sidebarToggle');
  const sidebar = document.querySelector('.sidebar');
  const overlay = document.getElementById('sidebar-overlay');

  // Toggle sidebar when hamburger button is clicked
  if (sidebarToggle) {
    sidebarToggle.addEventListener('click', function(e) {
      e.stopPropagation();
      document.body.classList.toggle('sidebar-toggled');

      // Explicitly hide the hamburger button when sidebar is toggled
      if (document.body.classList.contains('sidebar-toggled')) {
        sidebarToggle.style.display = 'none';
      } else {
        sidebarToggle.style.display = '';
      }
    });
  }

  // Close sidebar when overlay is clicked
  if (overlay) {
    overlay.addEventListener('click', function() {
      document.body.classList.remove('sidebar-toggled');
      // Show hamburger button again
      if (sidebarToggle) {
        sidebarToggle.style.display = '';
      }
    });
  }

  // Close sidebar when a nav link is clicked on mobile
  const navLinks = document.querySelectorAll('.sidebar .nav-link:not(.dropdown-toggle)');
  navLinks.forEach(link => {
    link.addEventListener('click', function() {
      if (window.innerWidth <= 768) {
        document.body.classList.remove('sidebar-toggled');
        // Show hamburger button again
        if (sidebarToggle) {
          sidebarToggle.style.display = '';
        }
      }
    });
  });

  // Handle window resize
  window.addEventListener('resize', function() {
    if (window.innerWidth > 768 && document.body.classList.contains('sidebar-toggled')) {
      document.body.classList.remove('sidebar-toggled');
    }
  });

  // Initialize Bootstrap dropdowns
  const dropdownToggleList = document.querySelectorAll('.dropdown-toggle');
  dropdownToggleList.forEach(dropdownToggle => {
    // For sidebar dropdowns, we need to handle them differently
    if (dropdownToggle.closest('.sidebar')) {
      dropdownToggle.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        // Get the dropdown menu
        const dropdownMenu = this.nextElementSibling;

        // Toggle the dropdown menu
        if (dropdownMenu.classList.contains('show')) {
          dropdownMenu.classList.remove('show');
        } else {
          // Close all other dropdowns first
          document.querySelectorAll('.sidebar .dropdown-menu.show').forEach(menu => {
            if (menu !== dropdownMenu) {
              menu.classList.remove('show');
            }
          });

          // Show this dropdown
          dropdownMenu.classList.add('show');
        }
      });
    }
  });

  // Close dropdowns when clicking outside
  document.addEventListener('click', function(e) {
    if (!e.target.closest('.dropdown-menu') && !e.target.classList.contains('dropdown-toggle')) {
      document.querySelectorAll('.sidebar .dropdown-menu.show').forEach(menu => {
        menu.classList.remove('show');
      });
    }
  });
});

// Also run this immediately to ensure sidebar is hidden on page load
if (window.innerWidth <= 768) {
  document.body.classList.remove('sidebar-toggled');
}
