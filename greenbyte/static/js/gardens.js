// Gardens JavaScript

// Function to update relative timestamps
function updateRelativeTimestamps() {
    const timestamps = document.querySelectorAll('.relative-timestamp');
    const now = new Date();

    timestamps.forEach(timestamp => {
        const timestampDate = new Date(timestamp.getAttribute('data-timestamp'));
        const timeDiff = Math.floor((now - timestampDate) / 1000); // in seconds

        let timeText = '';
        if (timeDiff < 60) {
            timeText = 'just now';
        } else if (timeDiff < 3600) {
            const mins = Math.floor(timeDiff / 60);
            timeText = `${mins} min${mins !== 1 ? 's' : ''} ago`;
        } else if (timeDiff < 86400) {
            const hours = Math.floor(timeDiff / 3600);
            timeText = `${hours} hour${hours !== 1 ? 's' : ''} ago`;
        } else if (timeDiff < 604800) {
            const days = Math.floor(timeDiff / 86400);
            timeText = `${days} day${days !== 1 ? 's' : ''} ago`;
        } else if (timeDiff < 2592000) {
            const weeks = Math.floor(timeDiff / 604800);
            timeText = `${weeks} week${weeks !== 1 ? 's' : ''} ago`;
        } else {
            // For older timestamps, keep the original date format
            timeText = timestamp.getAttribute('data-original-text');
        }

        timestamp.textContent = timeText;
    });
}

// Update timestamps every minute
setInterval(updateRelativeTimestamps, 60000);

document.addEventListener('DOMContentLoaded', function() {
    // Initialize timestamps when the page loads
    updateRelativeTimestamps();

    // Function to update plant status
    function updatePlantStatus(plantId, newStatus, csrfToken) {
        console.log('Updating plant', plantId, 'to status', newStatus);

        // Find the plant row and elements to update
        const link = document.querySelector(`a.status-update-link[data-plant-id="${plantId}"][data-status="${newStatus}"]`);
        if (!link) {
            console.error('Could not find status link');
            showToast('Error updating status: Could not find status link', 'bg-danger');
            return;
        }

        const plantRow = link.closest('.plant-row');
        const statusBadge = plantRow.querySelector('.col-3.d-flex .badge');
        const timestampElement = plantRow.querySelector('.col-2.text-muted small');
        const dropdownMenu = plantRow.querySelector('.dropdown-menu');
        const dropdownToggle = plantRow.querySelector('.dropdown-toggle');

        // Show loading indicator
        statusBadge.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Updating...';

        // Send AJAX request
        fetch(`/api/plant/${plantId}/status/${newStatus}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update the status badge with new status
                statusBadge.className = `badge ${data.style.bg} d-inline-flex align-items-center gap-1`;
                statusBadge.style.cssText = `
                    font-size: 0.8rem;
                    padding: 0.35rem 0.75rem;
                    ${data.style.extra_style}
                    white-space: nowrap;
                    max-width: fit-content;
                `;
                statusBadge.innerHTML = `
                    <i class="fas fa-${data.style.icon}"></i>
                    ${data.status}
                `;

                // Update the timestamp
                if (timestampElement && data.timestamp) {
                    timestampElement.innerHTML = '<i class="far fa-clock me-1"></i>just now';
                }

                // Update the dropdown menu
                if (dropdownMenu) {
                    // Update the dropdown toggle text
                    if (dropdownToggle) {
                        dropdownToggle.textContent = data.status;
                    }

                    // Clear the dropdown menu
                    dropdownMenu.innerHTML = '';

                    // Use the valid statuses from the server response
                    const validStatuses = data.valid_statuses || ['Seedling', 'Growing', 'Flowering', 'Fruiting', 'Harvested'];

                    // Add all statuses in order, making the current one non-clickable
                    validStatuses.forEach(status => {
                        const statusItem = document.createElement('li');

                        if (status === data.status) {
                            // Current status - non-clickable with checkmark
                            statusItem.innerHTML = `
                                <span class="dropdown-item py-1 text-muted d-flex align-items-center">
                                    <i class="fas fa-check me-2 text-success"></i>
                                    ${status}
                                </span>
                            `;
                        } else {
                            // Other status - clickable
                            statusItem.innerHTML = `
                                <a class="dropdown-item py-1 status-update-link"
                                   href="#"
                                   data-plant-id="${plantId}"
                                   data-status="${status}"
                                   data-csrf-token="${csrfToken}">
                                    ${status}
                                </a>
                            `;

                            // Add event listener to the new link
                            setTimeout(() => {
                                const newLink = statusItem.querySelector('.status-update-link');
                                if (newLink) {
                                    newLink.addEventListener('click', function(e) {
                                        e.preventDefault();
                                        updatePlantStatus(plantId, status, csrfToken);
                                    });
                                }
                            }, 0);
                        }

                        dropdownMenu.appendChild(statusItem);
                    });
                }

                // Show success message
                showToast(data.message, 'bg-success');
            } else {
                // Show error message and revert to original status
                console.error('Error updating status:', data.error);
                showToast('Error updating status: ' + data.error, 'bg-danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('An error occurred while updating the status.', 'bg-danger');
        });
    }

    // Helper function to add event listeners to a plant row
    function addEventListenersToPlantRow(plantRow) {
        console.log('Adding event listeners to plant row', plantRow);

        // Add event listeners to status update links
        const statusLinks = plantRow.querySelectorAll('.status-update-link');
        console.log('Found', statusLinks.length, 'status update links');
        statusLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const plantId = this.getAttribute('data-plant-id');
                const newStatus = this.getAttribute('data-status');
                const csrfToken = this.getAttribute('data-csrf-token');
                updatePlantStatus(plantId, newStatus, csrfToken);
            });
        });

        // Add event listeners to move plant links
        const moveLinks = plantRow.querySelectorAll('.move-plant-link');
        console.log('Found', moveLinks.length, 'move plant links');
        moveLinks.forEach(link => {
            console.log('Adding click listener to move link', link);
            link.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Move link clicked');
                const plantId = this.getAttribute('data-plant-id');
                const zoneId = this.getAttribute('data-zone-id');
                const zoneName = this.getAttribute('data-zone-name');
                const csrfToken = this.getAttribute('data-csrf-token');
                console.log('Moving plant', plantId, 'to zone', zoneId);
                movePlant(plantId, zoneId, zoneName, csrfToken, this.closest('.plant-row'));
            });
        });
    }

    // Function to add a plant to a zone
    function addPlantToZone(data) {
        const newZonePlantsContainer = document.querySelector(`#zone-${data.new_zone_id} .plants-container`);
        if (!newZonePlantsContainer) return;

        // Check if the zone has the 'No plants' message
        const noPlantMessage = newZonePlantsContainer.querySelector('.text-center');
        if (noPlantMessage && noPlantMessage.querySelector('.text-muted')) {
            // Remove only the 'No plants' message
            noPlantMessage.remove();
        }

        // Check if the zone has table headers
        const hasHeaders = newZonePlantsContainer.querySelector('.plant-table-header');
        if (!hasHeaders) {
            // Add table headers
            const headers = document.createElement('div');
            headers.className = 'd-flex align-items-center py-2 px-4 border-bottom plant-table-header';
            headers.style.backgroundColor = 'rgba(28, 200, 138, 0.05)';
            headers.style.fontWeight = '500';
            headers.innerHTML = `
                <div class="col-3">Plant</div>
                <div class="col-2">Quantity</div>
                <div class="col-3">Status</div>
                <div class="col-2">Last Updated</div>
                <div class="col-2">Actions</div>
            `;
            newZonePlantsContainer.appendChild(headers);
        }

        // Create a temporary container to parse the HTML
        const tempContainer = document.createElement('div');
        tempContainer.innerHTML = data.plant_html;
        const newPlantRow = tempContainer.firstElementChild;

        // Explicitly set the data-zone-id attribute
        newPlantRow.setAttribute('data-zone-id', data.new_zone_id);
        console.log('Setting data-zone-id attribute to', data.new_zone_id);

        // Add the new plant row to the zone
        newZonePlantsContainer.appendChild(newPlantRow);

        // Add event listeners to the new plant row
        const newStatusLinks = newPlantRow.querySelectorAll('.status-update-link');
        newStatusLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const plantId = this.getAttribute('data-plant-id');
                const newStatus = this.getAttribute('data-status');
                const csrfToken = this.getAttribute('data-csrf-token');
                updatePlantStatus(plantId, newStatus, csrfToken);
            });
        });

        // Add event listeners to the new move plant links
        const newMoveLinks = newPlantRow.querySelectorAll('.move-plant-link');
        newMoveLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const plantId = this.getAttribute('data-plant-id');
                const zoneId = this.getAttribute('data-zone-id');
                const zoneName = this.getAttribute('data-zone-name');
                const csrfToken = this.getAttribute('data-csrf-token');
                movePlant(plantId, zoneId, zoneName, csrfToken, this.closest('.plant-row'));
            });
        });

        // Update relative timestamps
        updateRelativeTimestamps();
    }

    // Function to get CSRF token from meta tag
    function getCSRFToken() {
        const metaTag = document.querySelector('meta[name="csrf-token"]');
        if (metaTag) {
            return metaTag.getAttribute('content');
        }
        return '';
    }

    // Function to move a plant to a new zone
    function movePlant(plantId, zoneId, zoneName, csrfToken, plantRow) {
        // Get CSRF token from meta tag if not provided
        if (!csrfToken) {
            csrfToken = getCSRFToken();
            console.log('Got CSRF token from meta tag:', csrfToken);
        }
        // Get the current zone ID from the plant row
        const currentZoneId = plantRow.getAttribute('data-zone-id');
        console.log('Moving plant', plantId, 'from zone', currentZoneId, 'to zone', zoneId);

        // Don't try to move to the same zone
        if (zoneId == currentZoneId) {
            showToast(`Plant is already in ${zoneName}`, 'bg-info');
            return;
        }

        // Store original HTML for potential restoration
        const originalHtml = plantRow.innerHTML;
        console.log('Stored original HTML');

        // Show loading indicator
        plantRow.style.opacity = '0.5';
        plantRow.innerHTML = `
            <div class="col-12 text-center py-2">
                <i class="fas fa-spinner fa-spin me-2"></i> Moving plant to ${zoneName}...
            </div>
        `;

        // Send AJAX request
        console.log('Using CSRF token:', csrfToken);

        // Create FormData object
        const formData = new FormData();
        formData.append('csrf_token', csrfToken);

        fetch(`/api/plant/${plantId}/move/${zoneId}`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': csrfToken
            }
            // Don't include body for POST requests to this endpoint
        })
        .then(response => {
            console.log('Response status:', response.status);
            if (!response.ok) {
                return response.text().then(text => {
                    console.log('Error response text:', text);
                    try {
                        // Try to parse as JSON
                        const json = JSON.parse(text);
                        // If it's a status mismatch error, return the parsed JSON
                        if (json.status_mismatch) {
                            console.log('Status mismatch detected in response');
                            return json;
                        }
                        // Otherwise throw an error with the error message
                        throw new Error(json.error || `Server returned ${response.status}: ${response.statusText}`);
                    } catch (e) {
                        // If parsing fails, use the raw text
                        throw new Error(`Server returned ${response.status}: ${response.statusText}. Response: ${text}`);
                    }
                });
            }
            return response.json().catch(e => {
                console.error('Error parsing JSON:', e);
                throw new Error(`Error parsing server response: ${e.message}`);
            });
        })
        .then(data => {
            if (data.success) {
                console.log('Move successful', data);

                // Check if the plant is already in the target zone
                if (data.already_in_zone) {
                    console.log('Plant is already in this zone');
                    // Restore the plant row
                    plantRow.style.opacity = '1';
                    plantRow.innerHTML = originalHtml;

                    // Make sure the data-zone-id attribute is set correctly
                    plantRow.setAttribute('data-zone-id', data.new_zone_id);
                    console.log('Set data-zone-id attribute to', data.new_zone_id);

                    // Re-add event listeners
                    addEventListenersToPlantRow(plantRow);

                    // Show message
                    showToast(data.message, 'bg-info');
                    return;
                }

                // Remove the plant row with animation
                plantRow.style.transition = 'all 0.5s ease';
                plantRow.style.maxHeight = '0';
                plantRow.style.opacity = '0';
                plantRow.style.overflow = 'hidden';

                setTimeout(() => {
                    // Remove the row after animation
                    if (plantRow.parentNode) {
                        plantRow.parentNode.removeChild(plantRow);
                    }

                    // Check if there are no more plants in the zone
                    const plantsContainer = document.querySelector(`#zone-${data.old_zone_id} .plants-container`);
                    if (plantsContainer && plantsContainer.querySelectorAll('.plant-row').length === 0) {
                        plantsContainer.innerHTML = `
                            <div class="text-center py-2">
                                <small class="text-muted">No plants in this zone yet</small>
                            </div>
                        `;
                    }

                    // Add the plant to the new zone
                    addPlantToZone(data);

                    // Show success message
                    showToast(data.message, 'bg-success');
                }, 500);
            } else {
                // Show error message
                console.error('Error moving plant:', data.error);

                // Restore the original plant row
                plantRow.style.opacity = '1';
                plantRow.innerHTML = originalHtml;

                // Make sure the data-zone-id attribute is preserved
                // We don't change it since the move failed
                console.log('Preserving original data-zone-id attribute:', plantRow.getAttribute('data-zone-id'));

                // Re-add event listeners
                addEventListenersToPlantRow(plantRow);

                // Check if this is a status mismatch error
                if (data.status_mismatch) {
                    console.log('Status mismatch error:', data.current_status, 'not in', data.available_statuses);
                    // Show a more informative error message
                    showToast(data.error, 'bg-warning');
                } else {
                    // Show generic error message
                    showToast('Error moving plant: ' + data.error, 'bg-danger');
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);

            // Restore the original plant row
            plantRow.style.opacity = '1';
            plantRow.innerHTML = originalHtml;

            // Make sure the data-zone-id attribute is preserved
            // We don't change it since the move failed
            console.log('Preserving original data-zone-id attribute:', plantRow.getAttribute('data-zone-id'));

            // Re-add event listeners
            addEventListenersToPlantRow(plantRow);

            // Show detailed error message
            let errorMessage = 'An error occurred while moving the plant.';
            if (error.message) {
                errorMessage += ' ' + error.message;
            }
            console.log('Showing error toast with message:', errorMessage);
            showToast(errorMessage, 'bg-danger');
        });
    }

    // Add event listeners to all status update links
    const statusLinks = document.querySelectorAll('.status-update-link');
    statusLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const plantId = this.getAttribute('data-plant-id');
            const newStatus = this.getAttribute('data-status');
            const csrfToken = this.getAttribute('data-csrf-token');
            updatePlantStatus(plantId, newStatus, csrfToken);
        });
    });

    // Add event listeners to all move plant links
    const moveLinks = document.querySelectorAll('.move-plant-link');
    moveLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const plantId = this.getAttribute('data-plant-id');
            const zoneId = this.getAttribute('data-zone-id');
            const zoneName = this.getAttribute('data-zone-name');
            const csrfToken = this.getAttribute('data-csrf-token');
            movePlant(plantId, zoneId, zoneName, csrfToken, this.closest('.plant-row'));
        });
    });

    // Make these functions available globally
    window.updatePlantStatus = updatePlantStatus;
    window.movePlant = movePlant;
    window.addEventListenersToPlantRow = addEventListenersToPlantRow;
    window.addPlantToZone = addPlantToZone;
    window.getCSRFToken = getCSRFToken;
    window.updateRelativeTimestamps = updateRelativeTimestamps;
});
