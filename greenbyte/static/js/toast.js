// Toast Notifications JavaScript

// Function to show toast notifications
function showToast(title, message, type = 'success') {
    // Get or create toast container
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }

    // Create toast element
    const toast = document.createElement('div');
    toast.className = 'toast';
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');
    toast.classList.add('toast-' + type);

    // Set icon based on type
    let icon = 'fas fa-check-circle';
    if (type === 'danger') {
        icon = 'fas fa-exclamation-circle';
    } else if (type === 'warning') {
        icon = 'fas fa-exclamation-triangle';
    } else if (type === 'info') {
        icon = 'fas fa-info-circle';
    }

    // Create toast content
    toast.innerHTML = `
        <div class="toast-header">
            <i class="${icon} me-2"></i>
            <strong class="me-auto">${title}</strong>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body">
            ${message}
        </div>
    `;

    // Add to container
    toastContainer.appendChild(toast);

    // Initialize and show toast
    const bsToast = new bootstrap.Toast(toast, {
        animation: true,
        autohide: true,
        delay: 5000
    });

    // Add entrance animation
    toast.style.opacity = '0';
    toast.style.transform = 'translateY(-20px)';
    toast.style.transition = 'opacity 0.3s ease, transform 0.3s ease';

    bsToast.show();

    // Animate in
    setTimeout(() => {
        toast.style.opacity = '1';
        toast.style.transform = 'translateY(0)';
    }, 50);

    // Remove from DOM after hiding
    toast.addEventListener('hidden.bs.toast', function() {
        toast.remove();
    });
}

// Function to handle sharing a post
function sharePost(button) {
    const postUrl = button.getAttribute('data-post-url');
    const shareLink = document.getElementById('shareLink');
    shareLink.value = postUrl;

    // Configure and show the toast
    const shareToastEl = document.getElementById('shareToast');
    const shareToast = new bootstrap.Toast(shareToastEl, {
        animation: true,
        autohide: true,
        delay: 5000
    });

    // Add a subtle entrance animation
    shareToastEl.style.opacity = '0';
    shareToastEl.style.transform = 'translateY(-20px)';
    shareToastEl.style.transition = 'opacity 0.3s ease, transform 0.3s ease';

    shareToast.show();

    // Animate in after a tiny delay
    setTimeout(() => {
        shareToastEl.style.opacity = '1';
        shareToastEl.style.transform = 'translateY(0)';
    }, 50);

    // Select the text for easy copying
    shareLink.select();
}

// Function to copy the share link
function copyShareLink() {
    const shareLink = document.getElementById('shareLink');
    shareLink.select();
    document.execCommand('copy');

    // Change the button text temporarily with a subtle animation
    const copyButton = document.querySelector('#shareLink + button');
    const originalHTML = copyButton.innerHTML;
    const originalBg = copyButton.style.backgroundColor;

    // Prepare for animation
    copyButton.style.transition = 'all 0.3s ease';

    // Animate to success state
    copyButton.innerHTML = '<i class="fas fa-check"></i>';
    copyButton.style.backgroundColor = 'rgba(19, 141, 97, 0.2)';
    copyButton.style.borderColor = 'rgba(19, 141, 97, 0.5)';
    copyButton.style.color = '#138d61';

    // Add a subtle feedback message
    const feedbackMsg = document.querySelector('.toast-body small');
    const originalMsg = feedbackMsg.innerHTML;
    feedbackMsg.innerHTML = 'Link copied to clipboard!';
    feedbackMsg.style.color = '#138d61';
    feedbackMsg.style.transition = 'color 0.3s ease';

    // Reset after 2 seconds with animation
    setTimeout(() => {
        copyButton.innerHTML = originalHTML;
        copyButton.style.backgroundColor = originalBg;
        copyButton.style.borderColor = 'rgba(28, 200, 138, 0.2)';

        // Reset feedback message
        feedbackMsg.innerHTML = originalMsg;
        feedbackMsg.style.color = '';
    }, 2000);
}
