// Garden Actions JavaScript

// Status update functionality
function updatePlantStatus(plantId, newStatus, csrfToken) {
    console.log('Updating plant', plantId, 'to status', newStatus);

    // Find the plant row
    const plantRow = document.querySelector(`.plant-row[data-plant-id="${plantId}"]`);
    if (!plantRow) {
        console.error('Could not find plant row');
        alert('Error updating status: Could not find plant row');
        return;
    }

    // Find elements to update
    const statusBadge = plantRow.querySelector('.col-3 .badge');
    const timestampElement = plantRow.querySelector('.col-2.text-muted small');

    // Show loading indicator
    if (statusBadge) {
        statusBadge.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Updating...';
    }

    // Show alert
    alert('Updating plant status to ' + newStatus);

    // Send AJAX request
    fetch(`/api/plant/${plantId}/status/${newStatus}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update the status badge with new status
            if (statusBadge) {
                statusBadge.className = `badge ${data.style.bg} d-inline-flex align-items-center gap-1`;
                statusBadge.style.cssText = `
                    font-size: 0.8rem;
                    padding: 0.35rem 0.75rem;
                    ${data.style.extra_style}
                    white-space: nowrap;
                    max-width: fit-content;
                `;
                statusBadge.innerHTML = `
                    <i class="fas fa-${data.style.icon}"></i>
                    ${data.status}
                `;
            }

            // Update the timestamp
            if (timestampElement) {
                timestampElement.innerHTML = '<i class="far fa-clock me-1"></i>just now';
            }

            // Show success message
            alert(data.message);

            // Close any open dropdowns
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                menu.style.display = 'none';
            });

            // Reload the page to ensure consistent state
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            // Show error message and revert to original status
            console.error('Error updating status:', data.error);
            alert('Error updating status: ' + data.error);

            // Reload the page to ensure consistent state
            setTimeout(() => {
                location.reload();
            }, 1000);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the status.');

        // Reload the page to ensure consistent state
        setTimeout(() => {
            location.reload();
        }, 1000);
    });
}

// Move plant functionality
function movePlant(plantId, zoneId, zoneName, csrfToken) {
    console.log('Moving plant', plantId, 'to zone', zoneId);

    // Show alert
    alert('Moving plant to ' + zoneName);

    // Send AJAX request
    fetch(`/api/plant/${plantId}/move/${zoneId}`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': csrfToken
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            alert(data.message);

            // Reload the page to ensure consistent state
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            // Show error message
            console.error('Error moving plant:', data.error);
            alert('Error moving plant: ' + data.error);

            // Reload the page to ensure consistent state
            setTimeout(() => {
                location.reload();
            }, 1000);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while moving the plant.');

        // Reload the page to ensure consistent state
        setTimeout(() => {
            location.reload();
        }, 1000);
    });
}

// Toggle status dropdown
function toggleStatusOptions(dropdownId) {
    console.log('Toggling status dropdown', dropdownId);
    const dropdown = document.getElementById(dropdownId);
    if (dropdown) {
        // Toggle this dropdown
        if (dropdown.style.display === 'block') {
            dropdown.style.display = 'none';
        } else {
            // Close all other dropdowns first
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                menu.style.display = 'none';
            });

            // Position the dropdown correctly
            const button = event.currentTarget;
            const rect = button.getBoundingClientRect();
            dropdown.style.position = 'fixed';
            dropdown.style.top = `${rect.bottom}px`;
            dropdown.style.left = `${rect.left}px`;
            dropdown.style.zIndex = '9999';

            // Show the dropdown
            dropdown.style.display = 'block';
        }
    }
}

// Toggle move dropdown
function toggleMoveOptions(dropdownId) {
    console.log('Toggling move dropdown', dropdownId);
    const dropdown = document.getElementById(dropdownId);
    if (dropdown) {
        // Toggle this dropdown
        if (dropdown.style.display === 'block') {
            dropdown.style.display = 'none';
        } else {
            // Close all other dropdowns first
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                menu.style.display = 'none';
            });

            // Position the dropdown correctly
            const button = event.currentTarget;
            const rect = button.getBoundingClientRect();
            dropdown.style.position = 'fixed';
            dropdown.style.top = `${rect.bottom}px`;
            dropdown.style.left = `${rect.left}px`;
            dropdown.style.zIndex = '9999';

            // Show the dropdown
            dropdown.style.display = 'block';
        }
    }
}

// Update TODO status
function updateTodoStatus(todoId, isCompleted, csrfToken) {
    console.log('Updating TODO', todoId, 'to', isCompleted ? 'completed' : 'not completed');

    // Show alert
    alert('Updating TODO status to ' + (isCompleted ? 'completed' : 'not completed'));

    // Send AJAX request
    fetch(`/api/calendar/event/${todoId}/complete`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify({
            completed: isCompleted
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            alert('TODO status updated successfully');

            // Reload the page to ensure consistent state
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            // Show error message
            console.error('Error updating TODO:', data.error);
            alert('Error updating TODO: ' + data.error);

            // Reload the page to ensure consistent state
            setTimeout(() => {
                location.reload();
            }, 1000);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the TODO.');

        // Reload the page to ensure consistent state
        setTimeout(() => {
            location.reload();
        }, 1000);
    });
}

// Initialize event listeners when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Garden actions script loaded');

    // Add event listeners to status buttons
    document.querySelectorAll('.status-toggle-btn').forEach(button => {
        button.addEventListener('click', function() {
            const dropdownId = this.getAttribute('data-dropdown-id');
            toggleStatusOptions(dropdownId);
        });
    });

    // Add event listeners to move buttons
    document.querySelectorAll('.move-toggle-btn').forEach(button => {
        button.addEventListener('click', function() {
            const dropdownId = this.getAttribute('data-dropdown-id');
            toggleMoveOptions(dropdownId);
        });
    });

    // Add event listeners to status update links
    document.querySelectorAll('.status-update-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const plantId = this.getAttribute('data-plant-id');
            const status = this.getAttribute('data-status');
            const csrfToken = this.getAttribute('data-csrf-token');
            updatePlantStatus(plantId, status, csrfToken);
        });
    });

    // Add event listeners to move plant links
    document.querySelectorAll('.move-plant-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const plantId = this.getAttribute('data-plant-id');
            const zoneId = this.getAttribute('data-zone-id');
            const zoneName = this.getAttribute('data-zone-name');
            const csrfToken = this.getAttribute('data-csrf-token');
            movePlant(plantId, zoneId, zoneName, csrfToken);
        });
    });

    // Add event listeners to TODO checkboxes
    document.querySelectorAll('.todo-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const todoId = this.getAttribute('data-todo-id');
            const csrfToken = this.getAttribute('data-csrf-token');
            const isCompleted = this.checked;
            updateTodoStatus(todoId, isCompleted, csrfToken);
        });
    });

    // Add click event to close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.dropdown')) {
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                menu.style.display = 'none';
            });
        }
    });
});
