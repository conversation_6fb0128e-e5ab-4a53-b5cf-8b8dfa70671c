// Zone Card JavaScript

// Toggle status dropdown visibility
function toggleStatusOptions(id) {
    const element = document.getElementById(id);
    if (element) {
        // Hide all other dropdown menus first
        document.querySelectorAll('.dropdown-menu').forEach(el => {
            if (el.id !== id) el.style.display = 'none';
        });

        // Toggle this menu
        if (element.style.display === 'none') {
            // Get the button position
            const button = document.querySelector(`button[onclick*="toggleStatusOptions('${id}')"]`);
            const rect = button.getBoundingClientRect();

            // Position the dropdown relative to the button
            element.style.display = 'block';
            element.style.zIndex = '9999';
            element.style.position = 'fixed';
            element.style.top = (rect.bottom + 5) + 'px';
            element.style.right = (window.innerWidth - rect.right) + 'px';
            element.style.backgroundColor = 'white';
            element.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
            element.style.border = '1px solid rgba(28, 200, 138, 0.2)';
            element.style.borderRadius = '0.5rem';
            element.style.minWidth = '150px';
        } else {
            element.style.display = 'none';
        }
    }
}

// Toggle move dropdown visibility
function toggleMoveOptions(id) {
    const element = document.getElementById(id);
    if (element) {
        // Hide all other dropdown menus first
        document.querySelectorAll('.dropdown-menu').forEach(el => {
            if (el.id !== id) el.style.display = 'none';
        });

        // Toggle this menu
        if (element.style.display === 'none') {
            // Get the button position
            const button = document.querySelector(`button[onclick*="toggleMoveOptions('${id}')"]`);
            const rect = button.getBoundingClientRect();

            // Position the dropdown relative to the button
            element.style.display = 'block';
            element.style.zIndex = '9999';
            element.style.position = 'fixed';
            element.style.top = (rect.bottom + 5) + 'px';
            element.style.right = (window.innerWidth - rect.right) + 'px';
            element.style.backgroundColor = 'white';
            element.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
            element.style.border = '1px solid rgba(28, 200, 138, 0.2)';
            element.style.borderRadius = '0.5rem';
            element.style.minWidth = '150px';
        } else {
            element.style.display = 'none';
        }
    }
}

// Update plant status
function updatePlantStatus(plantId, newStatus, csrfToken) {
    console.log('Updating plant status:', plantId, 'to', newStatus);

    // Find the plant row
    const plantRow = document.querySelector(`.plant-row[data-plant-id="${plantId}"]`);
    if (!plantRow) {
        console.error('Plant row not found for ID:', plantId);
        return;
    }

    // Find the status badge
    const statusBadge = plantRow.querySelector('.col-3 .badge');
    if (!statusBadge) {
        console.error('Status badge not found for plant ID:', plantId);
        return;
    }

    // Show loading state
    const originalBadgeHTML = statusBadge.innerHTML;
    statusBadge.innerHTML = `<i class="fas fa-spinner fa-spin"></i> Updating...`;

    // Send AJAX request
    fetch(`/api/plant/${plantId}/status/${newStatus}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        }
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update the status badge with new status
                let iconClass = 'fa-seedling'; // Default icon
                let badgeClass = 'bg-success'; // Default color

                // Determine icon and color based on status
                switch (newStatus.toLowerCase()) {
                    case 'seedling':
                        iconClass = 'fa-seedling';
                        badgeClass = 'bg-success';
                        break;
                    case 'growing':
                        iconClass = 'fa-leaf';
                        badgeClass = 'bg-info';
                        break;
                    case 'mature':
                        iconClass = 'fa-tree';
                        badgeClass = 'bg-primary';
                        break;
                    case 'harvested':
                        iconClass = 'fa-check';
                        badgeClass = 'bg-warning';
                        break;
                    case 'dormant':
                        iconClass = 'fa-moon';
                        badgeClass = 'bg-secondary';
                        break;
                    case 'diseased':
                        iconClass = 'fa-biohazard';
                        badgeClass = 'bg-danger';
                        break;
                    case 'dead':
                        iconClass = 'fa-skull';
                        badgeClass = 'bg-dark';
                        break;
                }

                // Update badge class
                statusBadge.className = statusBadge.className.replace(/bg-\w+/g, badgeClass);

                // Update badge content
                statusBadge.innerHTML = `<i class="fas ${iconClass}"></i> ${newStatus}`;

                // Update the timestamp to 'just now'
                const timestampElement = plantRow.querySelector('.col-2.text-muted small');
                if (timestampElement) {
                    timestampElement.innerHTML = '<i class="far fa-clock me-1"></i>just now';
                }

                // Update dropdown to show current status
                const dropdown = plantRow.querySelector('.dropdown:first-child .dropdown-menu');
                if (dropdown) {
                    const items = dropdown.querySelectorAll('li');
                    items.forEach(item => {
                        const link = item.querySelector('a');
                        const span = item.querySelector('span');

                        if (link && link.textContent.trim() === newStatus) {
                            // Convert link to current status indicator
                            item.innerHTML = `
                            <span class="dropdown-item py-1 text-muted d-flex align-items-center">
                                <i class="fas fa-check me-2 text-success"></i>
                                ${newStatus}
                            </span>
                        `;
                        } else if (span && span.textContent.trim().includes(newStatus)) {
                            // Already the current status
                        } else if (span && span.querySelector('.fa-check')) {
                            // Convert current status indicator back to link
                            const statusText = span.textContent.trim();
                            item.innerHTML = `
                            <a class="dropdown-item py-1 status-update-link"
                               href="#"
                               onclick="event.preventDefault(); updatePlantStatus('${plantId}', '${statusText}', '${csrfToken}');">
                                ${statusText}
                            </a>
                        `;
                        }
                    });
                }

                // Don't show a toast here - it's already shown by the original function
            } else {
                // Restore original badge on error
                statusBadge.innerHTML = originalBadgeHTML;
                // Don't show a toast here - it's already shown by the original function
            }

            // Hide the dropdown
            const dropdown = plantRow.querySelector('.dropdown:first-child .dropdown-menu');
            if (dropdown) {
                dropdown.style.display = 'none';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            // Restore original badge on error
            statusBadge.innerHTML = originalBadgeHTML;
            // Don't show a toast here - it's already shown by the original function
        });
}

// Move plant to another zone
function movePlant(plantId, zoneId, zoneName, csrfToken) {
    console.log('Moving plant:', plantId, 'to zone:', zoneId, zoneName);

    // Find the plant row
    const plantRow = document.querySelector(`.plant-row[data-plant-id="${plantId}"]`);
    if (!plantRow) {
        console.error('Plant row not found for ID:', plantId);
        return;
    }

    // Store original content and plant data
    const originalContent = plantRow.innerHTML;
    const plantName = plantRow.querySelector('.col-3 span').textContent.trim();
    const plantVariety = plantRow.querySelector('.col-3 small')?.textContent.trim() || '';
    const plantQuantity = plantRow.querySelector('.col-2 .badge').textContent.trim().replace(/[^0-9]/g, '');
    const plantStatus = plantRow.querySelector('.col-3 .badge').textContent.trim();
    const plantStatusIcon = plantRow.querySelector('.col-3 .badge i').className.replace('fas fa-', '');
    const plantStatusClass = Array.from(plantRow.querySelector('.col-3 .badge').classList)
        .find(cls => cls.startsWith('bg-')) || 'bg-success';
    const lastUpdated = plantRow.querySelector('.col-2.text-muted small').innerHTML;

    // Show loading state
    plantRow.style.opacity = '0.7';
    plantRow.style.transition = 'opacity 0.3s';
    plantRow.innerHTML = `
        <div class="col-12 text-center py-3">
            <i class="fas fa-spinner fa-spin me-2"></i> Moving plant to ${zoneName}...
        </div>
    `;

    // Send AJAX request
    fetch(`/api/plant/${plantId}/move/${zoneId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        }
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Get the current zone ID
                const currentZoneId = data.old_zone_id || plantRow.closest('.zone-content').getAttribute('data-zone-id');

                // Get the current zone container
                const currentZoneContainer = document.querySelector(`#zone-${currentZoneId} .plants-container`);

                // Get the target zone container
                const targetZoneContainer = document.querySelector(`#zone-${zoneId} .plants-container`);

                if (!currentZoneContainer || !targetZoneContainer) {
                    console.error('Zone containers not found');
                    // Fallback to page reload
                    window.location.reload();
                    return;
                }

                // Animate the removal
                plantRow.style.opacity = '0';
                plantRow.style.height = '0';
                plantRow.style.overflow = 'hidden';
                plantRow.style.transition = 'opacity 0.5s, height 0.5s';

                // After animation, remove from current zone and add to target zone
                setTimeout(() => {
                    // Remove from current zone
                    if (plantRow.parentNode) {
                        plantRow.parentNode.removeChild(plantRow);
                    }

                    // Check if current zone is now empty
                    if (currentZoneContainer.querySelectorAll('.plant-row').length === 0) {
                        currentZoneContainer.innerHTML = `
                        <div class="text-center py-3">
                            <small class="text-muted">No plants in this zone yet</small>
                        </div>
                    `;
                    }

                    // Check if target zone has the 'no plants' message
                    const noPlantMessage = targetZoneContainer.querySelector('.text-muted');
                    if (noPlantMessage && noPlantMessage.closest('.text-center')) {
                        // Clear the 'no plants' message
                        targetZoneContainer.innerHTML = '';
                    }

                    // Check if the target zone has the column headers
                    if (!targetZoneContainer.querySelector('.plant-table-header')) {
                        // Add column headers
                        const headerRow = document.createElement('div');
                        headerRow.className = 'd-flex align-items-center py-2 px-4 border-bottom plant-table-header';
                        headerRow.style.backgroundColor = 'rgba(28, 200, 138, 0.05)';
                        headerRow.style.fontWeight = '500';
                        headerRow.innerHTML = `
                        <div class="col-3">Plant</div>
                        <div class="col-2">Quantity</div>
                        <div class="col-3">Status</div>
                        <div class="col-2">Last Updated</div>
                        <div class="col-2">Actions</div>
                    `;
                        targetZoneContainer.appendChild(headerRow);
                    }

                    // Create a new plant row for the target zone
                    const newPlantRow = document.createElement('div');
                    newPlantRow.className = 'plant-row d-flex align-items-center py-2 px-4 border-bottom';
                    newPlantRow.style.borderColor = 'rgba(28, 200, 138, 0.1) !important';
                    newPlantRow.style.minWidth = '650px';
                    newPlantRow.style.transition = 'background-color 0.2s ease';
                    newPlantRow.style.opacity = '0';
                    newPlantRow.setAttribute('data-plant-id', plantId);

                    // Get all zones except the current one
                    const availableZones = [];
                    // First try to get zones from existing move links
                    const existingMoveLinks = document.querySelectorAll(`#zone-${zoneId} .move-plant-link`);

                    if (existingMoveLinks.length > 0) {
                        existingMoveLinks.forEach(link => {
                            const zId = link.getAttribute('data-zone-id');
                            const zName = link.getAttribute('data-zone-name');
                            if (zId && zName) {
                                availableZones.push({ id: zId, name: zName });
                            }
                        });
                    } else {
                        // If no existing move links (empty zone), get zones from all garden zones
                        document.querySelectorAll('.zone-header').forEach(zoneHeader => {
                            const zId = zoneHeader.getAttribute('data-zone-id');
                            const zName = zoneHeader.querySelector('.zone-name').textContent.trim();
                            if (zId && zId !== zoneId && zName) { // Exclude current zone
                                availableZones.push({ id: zId, name: zName });
                            }
                        });
                    }

                    // Get the statuses for the target zone
                    let zoneStatuses = [];
                    const existingStatusLinks = document.querySelectorAll(`#zone-${zoneId} .status-update-link`);

                    if (existingStatusLinks.length > 0) {
                        existingStatusLinks.forEach(link => {
                            const status = link.getAttribute('data-status');
                            if (status && !zoneStatuses.includes(status)) {
                                zoneStatuses.push(status);
                            }
                        });
                    } else {
                        // If no existing status links (empty zone), use common statuses or get from zone data
                        const zoneElement = document.querySelector(`#zone-${zoneId}`);
                        if (zoneElement && zoneElement.hasAttribute('data-statuses')) {
                            try {
                                zoneStatuses = JSON.parse(zoneElement.getAttribute('data-statuses'));
                            } catch (e) {
                                console.error('Error parsing zone statuses:', e);
                                // Fallback to common statuses
                                zoneStatuses = ['Seedling', 'Growing', 'Mature', 'Harvested'];
                            }
                        } else {
                            // Fallback to common statuses
                            zoneStatuses = ['Seedling', 'Growing', 'Mature', 'Harvested'];
                        }
                    }

                    // Build the new plant row HTML
                    newPlantRow.innerHTML = `
                    <!-- Plant Name & Variety -->
                    <div class="col-3 d-flex align-items-center gap-2">
                        <span style="color: #1cc88a; font-weight: 500;">
                            ${plantName}
                        </span>
                        ${plantVariety ? `<small class="text-muted">${plantVariety}</small>` : ''}
                    </div>

                    <!-- Quantity -->
                    <div class="col-2">
                        <span class="badge bg-light text-success">
                            <i class="fas fa-seedling"></i> ${plantQuantity}
                        </span>
                    </div>

                    <!-- Status -->
                    <div class="col-3 d-flex">
                        <span class="badge ${plantStatusClass} d-inline-flex align-items-center gap-1"
                              style="font-size: 0.8rem;
                                     padding: 0.35rem 0.75rem;
                                     white-space: nowrap;
                                     max-width: fit-content;">
                            <i class="fas fa-${plantStatusIcon}"></i>
                            ${plantStatus}
                        </span>
                    </div>

                    <!-- Last Updated -->
                    <div class="col-2 text-muted">
                        <small><i class="far fa-clock me-1"></i>just now</small>
                    </div>

                    <!-- Actions -->
                    <div class="col-2 d-flex gap-1">
                        <div class="dropdown">
                            <button class="btn btn-sm btn-light px-2 py-1" onclick="toggleStatusOptions('status-dropdown-${plantId}')">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                            <ul id="status-dropdown-${plantId}" class="dropdown-menu dropdown-menu-end" style="display: none;">
                                ${zoneStatuses.map(status => `
                                    <li>
                                        ${status === plantStatus ? `
                                            <span class="dropdown-item py-1 text-muted d-flex align-items-center">
                                                <i class="fas fa-check me-2 text-success"></i>
                                                ${status}
                                            </span>
                                        ` : `
                                            <a class="dropdown-item py-1 status-update-link"
                                               href="#"
                                               onclick="event.preventDefault(); updatePlantStatus('${plantId}', '${status}', '${csrfToken}');"
                                               data-plant-id="${plantId}"
                                               data-status="${status}"
                                               data-csrf-token="${csrfToken}">
                                                ${status}
                                            </a>
                                        `}
                                    </li>
                                `).join('')}
                            </ul>
                        </div>
                        ${availableZones.length > 0 ? `
                            <div class="dropdown">
                                <button class="btn btn-sm btn-light px-2 py-1" onclick="toggleMoveOptions('move-dropdown-${plantId}')">
                                    <i class="fas fa-exchange-alt"></i>
                                </button>
                                <ul id="move-dropdown-${plantId}" class="dropdown-menu dropdown-menu-end" style="display: none;">
                                    ${availableZones.map(zone => `
                                        <li>
                                            <a class="dropdown-item py-1 move-plant-link"
                                               href="#"
                                               onclick="event.preventDefault(); movePlant('${plantId}', '${zone.id}', '${zone.name}', '${csrfToken}');"
                                               data-plant-id="${plantId}"
                                               data-zone-id="${zone.id}"
                                               data-zone-name="${zone.name}"
                                               data-csrf-token="${csrfToken}">
                                                ${zone.name}
                                            </a>
                                        </li>
                                    `).join('')}
                                </ul>
                            </div>
                        ` : ''}
                    </div>
                `;

                    // Add to target zone
                    targetZoneContainer.appendChild(newPlantRow);

                    // Fade in the new row
                    setTimeout(() => {
                        newPlantRow.style.opacity = '1';
                        newPlantRow.style.transition = 'opacity 0.5s';
                    }, 50);

                    // Show success toast
                    window.showToast(`Plant moved to "${zoneName}"`, 'bg-success');
                }, 500);
            } else {
                // Restore original content on error
                plantRow.innerHTML = originalContent;
                plantRow.style.opacity = '1';

                // Show error toast
                window.showToast('Error: ' + (data.error || 'Failed to move plant'), 'bg-danger');
            }

            // Hide the dropdown
            const dropdown = document.querySelector(`.dropdown-menu[id^="move-dropdown-${plantId}"]`);
            if (dropdown) {
                dropdown.style.display = 'none';
            }
        })
        .catch(error => {
            console.error('Error:', error);

            // Restore original content on error
            plantRow.innerHTML = originalContent;
            plantRow.style.opacity = '1';

            // Show error toast
            window.showToast('An error occurred while moving the plant', 'bg-danger');
        });
}

// Close dropdowns when clicking outside
document.addEventListener('click', function (event) {
    // Check if the click was outside any dropdown
    if (!event.target.closest('.dropdown')) {
        document.querySelectorAll('.dropdown-menu').forEach(el => {
            el.style.display = 'none';
        });
    }
});

// Make these functions available globally
window.toggleStatusOptions = toggleStatusOptions;
window.toggleMoveOptions = toggleMoveOptions;
