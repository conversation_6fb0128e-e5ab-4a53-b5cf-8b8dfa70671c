// Events JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Load more events functionality
    const loadMoreBtn = document.getElementById('load-more-events');
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', function() {
            const hiddenEvents = document.getElementById('hidden-events');
            const eventsList = document.getElementById('events-list');
            const hiddenEventItems = hiddenEvents.querySelectorAll('.event-item');

            // Get the first 5 hidden events and append them to the visible list
            let count = 0;
            hiddenEventItems.forEach(item => {
                if (count < 5) {
                    const clone = item.cloneNode(true);
                    eventsList.appendChild(clone);
                    item.remove();
                    count++;
                }
            });

            // Hide the load more button if no more events
            if (hiddenEvents.querySelectorAll('.event-item').length === 0) {
                loadMoreBtn.style.display = 'none';
            }
        });
    }
});
