// Farm Hub Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Get all content cards
    const allCards = document.querySelectorAll('.content-card');

    // Get all nav items
    const navItems = document.querySelectorAll('#commercialNavbar .nav-item');

    // Function to show a specific card and hide others
    function showCard(cardId) {
        // Hide all cards first
        allCards.forEach(card => {
            card.classList.add('d-none');
        });

        // Show the selected card
        const selectedCard = document.getElementById(cardId);
        if (selectedCard) {
            selectedCard.classList.remove('d-none');
        }

        // Update active state in navigation
        navItems.forEach(item => {
            item.classList.remove('active');
            const link = item.querySelector('.nav-link');
            if (link && link.getAttribute('data-target') === cardId) {
                item.classList.add('active');
            }
        });

        // Save the current view to localStorage
        localStorage.setItem('farmHubActiveCard', cardId);
    }

    // Add click event listeners to all nav links
    navItems.forEach(item => {
        const link = item.querySelector('.nav-link');
        if (link && link.getAttribute('data-target')) {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetCard = this.getAttribute('data-target');
                showCard(targetCard);
            });
        }
    });

    // Add click event listener to the navbar brand (Farm Hub Dashboard)
    const navbarBrand = document.querySelector('.navbar-brand');
    if (navbarBrand && navbarBrand.getAttribute('data-target')) {
        navbarBrand.addEventListener('click', function(e) {
            e.preventDefault();
            const targetCard = this.getAttribute('data-target');
            showCard(targetCard);
        });
    }

    // Load the previously selected card or default to dashboard
    const savedCard = localStorage.getItem('farmHubActiveCard') || 'dashboard-card';
    showCard(savedCard);
});
