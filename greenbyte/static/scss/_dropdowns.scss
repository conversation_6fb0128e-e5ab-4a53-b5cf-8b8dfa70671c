// Custom Dropdown Styling

.dropdown {
  .dropdown-menu {
    font-size: $dropdown-font-size;
    .dropdown-header {
      @extend .text-uppercase;
      font-weight: 800;
      font-size: 0.65rem;
      color: $gray-500;
    }
    .dropdown-item {
      &:hover {
        background: rgba(28, 200, 138, 0.1);
        color: #1cc88a;
      }
    }
  }
}

// Utility class to hide arrow from dropdown

.dropdown.no-arrow {
  .dropdown-toggle::after {
    display: none;
  }
}
